{"name": "farm-scheduler-system", "version": "1.0.0", "description": "AI-powered farm scheduling system with weather integration", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test-apis": "node src/test-apis.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "node scripts/seed.js", "db:studio": "prisma studio"}, "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@prisma/client": "^6.9.0", "axios": "^1.6.7", "chalk": "^5.4.1", "dotenv": "^16.4.5", "express": "^4.18.2", "googleapis": "^128.0.0", "moment": "^2.30.1", "openai": "^4.28.4", "prisma": "^6.9.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.3"}}