// Test script for temporal scheduling features

import axios from 'axios';

const API_BASE = 'http://localhost:3000/api';

// Test data
const testUserProfile = {
  phoneNumber: "+**********",
  location: {
    latitude: 40.7128,
    longitude: -74.0060,
    city: "New York",
    state: "New York",
    country: "United States"
  },
  locationContext: {
    location: {
      city: "New York",
      state: "New York", 
      country: "United States"
    },
    climate_zone: {
      zone_name: "Temperate Continental",
      zone_code: "Dfb"
    },
    current_season: {
      name: "Winter"
    }
  },
  preferences: {
    llm_provider: "claude"
  }
};

const testCropContext = {
  crop_info: {
    name: "Tomato",
    variety: "Roma",
    planting_date: "2024-03-15",
    current_stage: "Vegetative",
    days_since_planting: 90,
    expected_harvest_date: "2024-08-15"
  },
  stage_details: {
    stage_number: 2,
    stage_name: "Vegetative",
    week_in_stage: 4
  }
};

async function testSystemStatus() {
  console.log('\n🔍 Testing System Status...');
  try {
    const response = await axios.get(`${API_BASE}/system/status`);
    console.log('✅ System Status:', {
      llm_providers: response.data.data.available_providers,
      strict_mode: response.data.data.strict_mode,
      services_healthy: Object.values(response.data.data.services).filter(s => s.status === 'healthy').length
    });
  } catch (error) {
    console.error('❌ System status failed:', error.response?.data || error.message);
  }
}

async function testWeeklyScheduleWithDate() {
  console.log('\n📅 Testing Weekly Schedule with Target Date...');
  try {
    // Test current week
    const currentResponse = await axios.post(`${API_BASE}/schedule/week`, {
      userProfile: testUserProfile,
      cropContext: testCropContext,
      targetDate: "2024-06-15" // Future date
    });
    
    console.log('✅ Current week schedule generated');
    console.log('   Target Date:', currentResponse.data.data.week_info?.targetDate);
    console.log('   Week Range:', currentResponse.data.data.week_info?.date_range);
    console.log('   Activity:', currentResponse.data.data.activity?.title);
    
    // Test historical date
    const historicalResponse = await axios.post(`${API_BASE}/schedule/week`, {
      userProfile: testUserProfile,
      cropContext: testCropContext,
      targetDate: "2024-04-15" // Historical date
    });
    
    console.log('✅ Historical schedule generated');
    console.log('   Historical Date:', historicalResponse.data.data.week_info?.targetDate);
    console.log('   Is Historical:', historicalResponse.data.data.week_info?.isHistorical);
    
  } catch (error) {
    console.error('❌ Weekly schedule with date failed:', error.response?.data || error.message);
  }
}

async function testMultiWeekSchedule() {
  console.log('\n📊 Testing Multi-Week Schedule with Start Date...');
  try {
    const response = await axios.post(`${API_BASE}/schedule/multi-week`, {
      userProfile: testUserProfile,
      cropContext: testCropContext,
      numberOfWeeks: 3,
      startDate: "2024-06-01"
    });
    
    console.log('✅ Multi-week schedule generated');
    console.log('   Total Weeks:', response.data.data.summary?.total_weeks);
    console.log('   Date Range:', response.data.data.summary?.date_range);
    console.log('   Schedules Generated:', response.data.data.schedules?.length);
    console.log('   Includes Future:', response.data.data.summary?.includes_future);
    
  } catch (error) {
    console.error('❌ Multi-week schedule failed:', error.response?.data || error.message);
  }
}

async function testDateRangeSchedule() {
  console.log('\n🗓️ Testing Date Range Schedule...');
  try {
    const dateList = [
      "2024-06-01",
      "2024-06-15", 
      "2024-07-01",
      "2024-07-15"
    ];
    
    const response = await axios.post(`${API_BASE}/schedule/date-range`, {
      userProfile: testUserProfile,
      cropContext: testCropContext,
      dateList: dateList
    });
    
    console.log('✅ Date range schedule generated');
    console.log('   Requested Dates:', response.data.data.summary?.total_requested);
    console.log('   Successful:', response.data.data.summary?.successful);
    console.log('   Failed:', response.data.data.summary?.failed);
    console.log('   Date Range:', response.data.data.summary?.date_range);
    
    if (response.data.data.errors?.length > 0) {
      console.log('   Errors:', response.data.data.errors);
    }
    
  } catch (error) {
    console.error('❌ Date range schedule failed:', error.response?.data || error.message);
  }
}

async function testDateValidation() {
  console.log('\n✅ Testing Date Validation...');
  try {
    // Test invalid date
    const invalidResponse = await axios.post(`${API_BASE}/schedule/week`, {
      userProfile: testUserProfile,
      cropContext: testCropContext,
      targetDate: "invalid-date"
    });
    
    console.log('❌ Should have failed with invalid date');
    
  } catch (error) {
    if (error.response?.status === 500 && error.response?.data?.details?.includes('Invalid target date')) {
      console.log('✅ Date validation working - rejected invalid date');
    } else {
      console.error('❌ Unexpected error:', error.response?.data || error.message);
    }
  }
  
  try {
    // Test very old date
    const oldDateResponse = await axios.post(`${API_BASE}/schedule/week`, {
      userProfile: testUserProfile,
      cropContext: testCropContext,
      targetDate: "2020-01-01"
    });
    
    console.log('❌ Should have failed with too old date');
    
  } catch (error) {
    if (error.response?.status === 500 && error.response?.data?.details?.includes('too far in the past')) {
      console.log('✅ Date validation working - rejected too old date');
    } else {
      console.error('❌ Unexpected error:', error.response?.data || error.message);
    }
  }
}

async function runAllTests() {
  console.log('🚀 Starting Temporal Scheduling Feature Tests\n');
  
  await testSystemStatus();
  await testWeeklyScheduleWithDate();
  await testMultiWeekSchedule();
  await testDateRangeSchedule();
  await testDateValidation();
  
  console.log('\n🎉 All tests completed!');
}

// Run tests
runAllTests().catch(console.error);
