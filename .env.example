# LLM Provider Configuration
# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Claude API Key (Anthropic)
CLAUDE_API_KEY=your_claude_api_key_here

# LLM Provider Selection
DEFAULT_LLM_PROVIDER=claude
<PERSON>_MODEL=gpt-4o-2024-08-06
CLAUDE_MODEL=claude-3-5-sonnet-latest

# Service Configuration
STRICT_MODE=true
FAIL_ON_MISSING_CORE_SERVICES=true

# OpenWeatherMap API Key (free tier available at openweathermap.org)
OPENWEATHER_API_KEY=your_openweather_api_key_here

# YouTube Data API v3 Key (from Google Cloud Console)
YOUTUBE_API_KEY=your_youtube_api_key_here

# Image Search APIs (choose one or more)
# Option 1: Google Custom Search API
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here

# Option 2: Bing Image Search API (better free tier)
BING_SEARCH_API_KEY=your_bing_search_api_key_here

# Option 3: Pexels API (free, high-quality)
PEXELS_API_KEY=your_pexels_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development
API_URL=http://localhost:3000/api

# Database (optional - for storing user profiles)
DATABASE_URL=

# OpenStreetMap Nominatim API (no key required)
# Rate limit: 1 request per second
NOMINATIM_USER_AGENT=farm-scheduler-v1.0
