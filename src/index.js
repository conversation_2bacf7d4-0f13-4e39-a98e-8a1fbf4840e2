// Main Express server

import express from 'express';
import dotenv from 'dotenv';
import { FarmSchedulerService } from './services/scheduler.js';
import { YouTubeService } from './services/youtube.js';
import { ImageSearchService } from './services/image-search.js';

dotenv.config();

const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

const schedulerService = new FarmSchedulerService();

// User registration endpoint
app.post('/api/register', async (req, res) => {
  try {
    const { phoneNumber, latitude, longitude, preferences = {} } = req.body;

    if (!phoneNumber || !latitude || !longitude) {
      return res.status(400).json({
        error: 'Phone number, latitude, and longitude are required'
      });
    }

    const userProfile = await schedulerService.processUserRegistration(
      phoneNumber,
      latitude,
      longitude,
      preferences
    );

    res.json({
      success: true,
      data: userProfile
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Failed to process registration',
      details: error.message
    });
  }
});

// Get crop context
app.post('/api/crop/context', async (req, res) => {
  try {
    const { userProfile, cropType, plantingDate } = req.body;
    
    if (!userProfile || !cropType || !plantingDate) {
      return res.status(400).json({ 
        error: 'User profile, crop type, and planting date are required' 
      });
    }

    const cropContext = await schedulerService.getCropContext(
      userProfile, 
      cropType, 
      plantingDate
    );

    res.json({ 
      success: true, 
      data: cropContext 
    });
  } catch (error) {
    console.error('Crop context error:', error);
    res.status(500).json({ 
      error: 'Failed to get crop context' 
    });
  }
});

// Generate weekly schedule with optional target date
app.post('/api/schedule/week', async (req, res) => {
  try {
    const { userProfile, cropContext, previousActivity, targetDate } = req.body;

    if (!userProfile || !cropContext) {
      return res.status(400).json({
        error: 'User profile and crop context are required'
      });
    }

    const schedule = await schedulerService.generateWeeklySchedule(
      userProfile,
      cropContext,
      previousActivity,
      targetDate
    );

    res.json({
      success: true,
      data: schedule
    });
  } catch (error) {
    console.error('Schedule generation error:', error);
    res.status(500).json({
      error: 'Failed to generate schedule',
      details: error.message
    });
  }
});

// Generate multiple weeks with optional start date
app.post('/api/schedule/multi-week', async (req, res) => {
  try {
    const { userProfile, cropContext, numberOfWeeks = 4, startDate } = req.body;

    if (!userProfile || !cropContext) {
      return res.status(400).json({
        error: 'User profile and crop context are required'
      });
    }

    const result = await schedulerService.generateMultiWeekSchedule(
      userProfile,
      cropContext,
      numberOfWeeks,
      startDate
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Multi-week schedule error:', error);
    res.status(500).json({
      error: 'Failed to generate multi-week schedule',
      details: error.message
    });
  }
});

// Generate schedule for specific date range (non-contiguous dates)
app.post('/api/schedule/date-range', async (req, res) => {
  try {
    const { userProfile, cropContext, dateList } = req.body;

    if (!userProfile || !cropContext || !dateList) {
      return res.status(400).json({
        error: 'User profile, crop context, and date list are required'
      });
    }

    if (!Array.isArray(dateList) || dateList.length === 0) {
      return res.status(400).json({
        error: 'Date list must be a non-empty array'
      });
    }

    const result = await schedulerService.generateDateRangeSchedule(
      userProfile,
      cropContext,
      dateList
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Date range schedule error:', error);
    res.status(500).json({
      error: 'Failed to generate date range schedule',
      details: error.message
    });
  }
});

// Check weather impact on a schedule
app.post('/api/schedule/weather-impact', async (req, res) => {
  try {
    const { userProfile, scheduleData, activityType } = req.body;

    if (!userProfile || !scheduleData) {
      return res.status(400).json({
        error: 'User profile and schedule data are required'
      });
    }

    const impact = await schedulerService.checkWeatherImpact(
      userProfile,
      scheduleData,
      activityType
    );

    res.json({
      success: true,
      data: impact
    });
  } catch (error) {
    console.error('Weather impact check error:', error);
    res.status(500).json({
      error: 'Failed to check weather impact',
      details: error.message
    });
  }
});

// Update schedule based on weather changes
app.put('/api/schedule/:scheduleId/weather-update', async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { userProfile, cropContext, weatherImpact } = req.body;

    if (!userProfile || !cropContext || !weatherImpact) {
      return res.status(400).json({
        error: 'User profile, crop context, and weather impact data are required'
      });
    }

    const result = await schedulerService.createWeatherUpdatedSchedule(
      scheduleId,
      userProfile,
      cropContext,
      weatherImpact
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Weather schedule update error:', error);
    res.status(500).json({
      error: 'Failed to update schedule based on weather',
      details: error.message
    });
  }
});

// Get weather monitoring status for a location
app.get('/api/weather/monitoring-status', async (req, res) => {
  try {
    const { latitude, longitude } = req.query;

    if (!latitude || !longitude) {
      return res.status(400).json({
        error: 'Latitude and longitude are required'
      });
    }

    const status = schedulerService.getWeatherMonitoringStatus(
      parseFloat(latitude),
      parseFloat(longitude)
    );

    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('Weather monitoring status error:', error);
    res.status(500).json({
      error: 'Failed to get weather monitoring status',
      details: error.message
    });
  }
});

// Get supported activities for weather impact analysis
app.get('/api/weather/supported-activities', (req, res) => {
  try {
    const activities = schedulerService.getSupportedActivities();

    res.json({
      success: true,
      data: activities
    });
  } catch (error) {
    console.error('Supported activities error:', error);
    res.status(500).json({
      error: 'Failed to get supported activities',
      details: error.message
    });
  }
});

// System status endpoint
app.get('/api/system/status', (req, res) => {
  try {
    const status = schedulerService.getSystemStatus();
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('System status error:', error);
    res.status(500).json({
      error: 'Failed to get system status',
      details: error.message
    });
  }
});

// LLM provider information endpoint
app.get('/api/system/providers', (req, res) => {
  try {
    const providerInfo = schedulerService.getProviderInfo();
    res.json({
      success: true,
      data: providerInfo
    });
  } catch (error) {
    console.error('Provider info error:', error);
    res.status(500).json({
      error: 'Failed to get provider information',
      details: error.message
    });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(port, () => {
  console.log(`Farm Scheduler API running on port ${port}`);
});

// Endpoint to get only pest and disease information
app.post('/api/pest-disease/alerts', async (req, res) => {
  try {
    const { cropContext, locationContext, currentWeather, userPreferences = {} } = req.body;

    if (!cropContext || !locationContext) {
      return res.status(400).json({
        error: 'Crop context and location context are required'
      });
    }

    const pestDiseaseInfo = await schedulerService.llmManager.generatePestDiseaseInfo(
      cropContext,
      locationContext,
      currentWeather || {},
      userPreferences.llm_provider
    );

    res.json({
      success: true,
      data: pestDiseaseInfo
    });
  } catch (error) {
    console.error('Pest/disease generation error:', error);
    res.status(500).json({
      error: 'Failed to generate pest and disease information',
      details: error.message
    });
  }
});

// Endpoint to get YouTube video recommendations
app.post('/api/learning/videos', async (req, res) => {
  try {
    const { cropContext, weeklyActivity, locationContext, userPreferences = {} } = req.body;

    if (!cropContext || !weeklyActivity || !locationContext) {
      return res.status(400).json({
        error: 'Crop context, weekly activity, and location context are required'
      });
    }

    // Get AI recommendations first
    const recommendations = await schedulerService.llmManager.generateYouTubeVideos(
      cropContext,
      weeklyActivity,
      locationContext,
      userPreferences.llm_provider
    );

    // Fetch actual YouTube videos or generate search URLs
    const youtubeService = new YouTubeService();
    let actualVideos = [];
    let searchUrls = [];

    if (youtubeService.isAvailable()) {
      actualVideos = await youtubeService.searchFarmingVideos(
        cropContext.crop_info.name,
        weeklyActivity.title,
        cropContext.crop_info.current_stage
      );
    } else {
      searchUrls = youtubeService.generateSearchUrls(
        cropContext.crop_info.name,
        weeklyActivity.title,
        cropContext.crop_info.current_stage
      );
    }

    // Generate web search suggestions
    const webSearchSuggestions = await schedulerService.llmManager.generateWebSearchSuggestions(
      weeklyActivity.title,
      cropContext.crop_info.name,
      locationContext.location.city,
      userPreferences.llm_provider
    );

    res.json({
      success: true,
      data: {
        videos: actualVideos,
        recommendations: recommendations.videos,
        search_urls: searchUrls,
        web_search_suggestions: webSearchSuggestions,
        message: actualVideos.length > 0
          ? 'Fetched actual YouTube videos'
          : youtubeService.isAvailable()
            ? 'No videos found, showing AI recommendations'
            : 'YouTube API not configured, showing search suggestions and AI recommendations'
      }
    });
  } catch (error) {
    console.error('YouTube video generation error:', error);
    res.status(500).json({
      error: 'Failed to fetch videos',
      details: error.message
    });
  }
});

// Endpoint to get pest/disease images
app.post('/api/pest-disease/images', async (req, res) => {
  try {
    const { pestName, cropName, symptoms } = req.body;
    
    if (!pestName || !cropName) {
      return res.status(400).json({ 
        error: 'Pest name and crop name are required' 
      });
    }

    const imageService = new ImageSearchService();
    let images = [];
    let searchSuggestions = null;

    if (imageService.isAvailable()) {
      images = await imageService.searchPestDiseaseImages(
        pestName,
        cropName,
        symptoms || []
      );
    } else {
      searchSuggestions = imageService.generateSearchSuggestions(
        pestName,
        cropName,
        symptoms || []
      );
    }

    const formattedImages = imageService.formatForGallery(images);

    res.json({
      success: true,
      data: {
        images: formattedImages,
        total: formattedImages.length,
        search_suggestions: searchSuggestions,
        message: images.length > 0
          ? 'Found images using configured APIs'
          : 'Image search APIs not configured, providing search suggestions'
      }
    });
  } catch (error) {
    console.error('Image search error:', error);
    res.status(500).json({ 
      error: 'Failed to fetch images' 
    });
  }
});
