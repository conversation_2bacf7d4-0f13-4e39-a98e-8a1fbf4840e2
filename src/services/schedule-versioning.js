// Schedule versioning and change tracking service

import moment from 'moment';

export class ScheduleVersioningService {
  constructor() {
    // In-memory storage for schedule versions (in production, use database)
    this.scheduleVersions = new Map(); // scheduleId -> versions array
    this.changeLog = new Map(); // scheduleId -> change log array
    this.activeSchedules = new Map(); // scheduleId -> current active version
    
    // Version metadata
    this.versionCounter = 0;
  }

  // Generate unique schedule ID
  generateScheduleId(userProfile, cropContext) {
    const userKey = userProfile.phoneNumber || userProfile.userId || 'anonymous';
    const cropKey = `${cropContext.crop_info.name}-${cropContext.crop_info.planting_date}`;
    const timestamp = Date.now();
    return `schedule_${userKey}_${cropKey}_${timestamp}`.replace(/[^a-zA-Z0-9_-]/g, '_');
  }

  // Generate version ID
  generateVersionId() {
    return `v${++this.versionCounter}_${Date.now()}`;
  }

  // Create initial schedule version
  createScheduleVersion(scheduleId, scheduleData, metadata = {}) {
    const versionId = this.generateVersionId();
    const version = {
      version_id: versionId,
      schedule_id: scheduleId,
      version_number: 1,
      schedule_data: JSON.parse(JSON.stringify(scheduleData)), // Deep copy
      metadata: {
        created_at: new Date().toISOString(),
        created_by: 'system',
        change_reason: 'initial_creation',
        weather_triggered: false,
        ...metadata
      },
      status: 'active',
      parent_version: null
    };

    // Initialize version history
    if (!this.scheduleVersions.has(scheduleId)) {
      this.scheduleVersions.set(scheduleId, []);
      this.changeLog.set(scheduleId, []);
    }

    // Store version
    this.scheduleVersions.get(scheduleId).push(version);
    this.activeSchedules.set(scheduleId, versionId);

    // Log creation
    this.logChange(scheduleId, {
      action: 'create',
      version_id: versionId,
      change_reason: 'initial_creation',
      timestamp: new Date().toISOString()
    });

    return version;
  }

  // Create new version from existing schedule (for updates)
  createUpdatedVersion(scheduleId, updatedScheduleData, changeReason, metadata = {}) {
    const versions = this.scheduleVersions.get(scheduleId);
    if (!versions || versions.length === 0) {
      throw new Error(`Schedule ${scheduleId} not found`);
    }

    const currentVersion = this.getCurrentVersion(scheduleId);
    const versionId = this.generateVersionId();
    const newVersionNumber = versions.length + 1;

    const newVersion = {
      version_id: versionId,
      schedule_id: scheduleId,
      version_number: newVersionNumber,
      schedule_data: JSON.parse(JSON.stringify(updatedScheduleData)), // Deep copy
      metadata: {
        created_at: new Date().toISOString(),
        created_by: 'system',
        change_reason: changeReason,
        weather_triggered: metadata.weather_triggered || false,
        weather_changes: metadata.weather_changes || null,
        ...metadata
      },
      status: 'active',
      parent_version: currentVersion.version_id
    };

    // Mark previous version as superseded
    currentVersion.status = 'superseded';
    currentVersion.superseded_at = new Date().toISOString();
    currentVersion.superseded_by = versionId;

    // Store new version
    versions.push(newVersion);
    this.activeSchedules.set(scheduleId, versionId);

    // Calculate and log changes
    const changes = this.calculateChanges(currentVersion.schedule_data, updatedScheduleData);
    this.logChange(scheduleId, {
      action: 'update',
      version_id: versionId,
      parent_version: currentVersion.version_id,
      change_reason: changeReason,
      changes: changes,
      timestamp: new Date().toISOString(),
      weather_triggered: metadata.weather_triggered || false
    });

    return newVersion;
  }

  // Get current active version of a schedule
  getCurrentVersion(scheduleId) {
    const activeVersionId = this.activeSchedules.get(scheduleId);
    if (!activeVersionId) {
      throw new Error(`No active version found for schedule ${scheduleId}`);
    }

    const versions = this.scheduleVersions.get(scheduleId);
    return versions.find(v => v.version_id === activeVersionId);
  }

  // Get specific version by version ID
  getVersion(scheduleId, versionId) {
    const versions = this.scheduleVersions.get(scheduleId);
    if (!versions) {
      throw new Error(`Schedule ${scheduleId} not found`);
    }

    const version = versions.find(v => v.version_id === versionId);
    if (!version) {
      throw new Error(`Version ${versionId} not found for schedule ${scheduleId}`);
    }

    return version;
  }

  // Get version history for a schedule
  getVersionHistory(scheduleId) {
    const versions = this.scheduleVersions.get(scheduleId);
    if (!versions) {
      throw new Error(`Schedule ${scheduleId} not found`);
    }

    return versions.map(version => ({
      version_id: version.version_id,
      version_number: version.version_number,
      created_at: version.metadata.created_at,
      change_reason: version.metadata.change_reason,
      weather_triggered: version.metadata.weather_triggered,
      status: version.status,
      parent_version: version.parent_version
    }));
  }

  // Calculate differences between two schedule versions
  calculateChanges(oldSchedule, newSchedule) {
    const changes = [];

    // Compare activities
    if (oldSchedule.activity && newSchedule.activity) {
      if (oldSchedule.activity.title !== newSchedule.activity.title) {
        changes.push({
          field: 'activity.title',
          old_value: oldSchedule.activity.title,
          new_value: newSchedule.activity.title,
          change_type: 'modification'
        });
      }

      if (oldSchedule.activity.priority !== newSchedule.activity.priority) {
        changes.push({
          field: 'activity.priority',
          old_value: oldSchedule.activity.priority,
          new_value: newSchedule.activity.priority,
          change_type: 'modification'
        });
      }

      // Compare tasks
      const oldTasks = oldSchedule.activity.tasks || [];
      const newTasks = newSchedule.activity.tasks || [];

      if (oldTasks.length !== newTasks.length) {
        changes.push({
          field: 'activity.tasks',
          old_value: `${oldTasks.length} tasks`,
          new_value: `${newTasks.length} tasks`,
          change_type: 'task_count_change'
        });
      }

      // Compare individual tasks
      newTasks.forEach((newTask, index) => {
        const oldTask = oldTasks[index];
        if (!oldTask) {
          changes.push({
            field: `activity.tasks[${index}]`,
            old_value: null,
            new_value: newTask.task,
            change_type: 'addition'
          });
        } else if (oldTask.task !== newTask.task) {
          changes.push({
            field: `activity.tasks[${index}]`,
            old_value: oldTask.task,
            new_value: newTask.task,
            change_type: 'modification'
          });
        }
      });
    }

    // Compare weather considerations
    if (oldSchedule.weather_considerations !== newSchedule.weather_considerations) {
      changes.push({
        field: 'weather_considerations',
        old_value: oldSchedule.weather_considerations,
        new_value: newSchedule.weather_considerations,
        change_type: 'modification'
      });
    }

    // Compare timing
    if (oldSchedule.timing && newSchedule.timing) {
      if (oldSchedule.timing.best_time !== newSchedule.timing.best_time) {
        changes.push({
          field: 'timing.best_time',
          old_value: oldSchedule.timing.best_time,
          new_value: newSchedule.timing.best_time,
          change_type: 'modification'
        });
      }
    }

    return changes;
  }

  // Log a change event
  logChange(scheduleId, changeEvent) {
    if (!this.changeLog.has(scheduleId)) {
      this.changeLog.set(scheduleId, []);
    }

    const log = this.changeLog.get(scheduleId);
    log.push({
      id: `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...changeEvent
    });

    // Keep only last 100 changes per schedule
    if (log.length > 100) {
      log.splice(0, log.length - 100);
    }
  }

  // Get change log for a schedule
  getChangeLog(scheduleId, limit = 50) {
    const log = this.changeLog.get(scheduleId) || [];
    return log.slice(-limit).reverse(); // Most recent first
  }

  // Check for update conflicts (if multiple updates happen simultaneously)
  checkUpdateConflicts(scheduleId, baseVersionId) {
    const currentVersion = this.getCurrentVersion(scheduleId);
    
    if (currentVersion.version_id !== baseVersionId) {
      return {
        has_conflict: true,
        current_version: currentVersion.version_id,
        expected_version: baseVersionId,
        message: 'Schedule has been updated since this change was initiated'
      };
    }

    return {
      has_conflict: false,
      current_version: currentVersion.version_id
    };
  }

  // Rollback to a previous version
  rollbackToVersion(scheduleId, targetVersionId, reason = 'manual_rollback') {
    const versions = this.scheduleVersions.get(scheduleId);
    if (!versions) {
      throw new Error(`Schedule ${scheduleId} not found`);
    }

    const targetVersion = versions.find(v => v.version_id === targetVersionId);
    if (!targetVersion) {
      throw new Error(`Version ${targetVersionId} not found`);
    }

    // Create new version based on target version data
    const rollbackVersion = this.createUpdatedVersion(
      scheduleId,
      targetVersion.schedule_data,
      reason,
      {
        rollback_to: targetVersionId,
        rollback_from: this.getCurrentVersion(scheduleId).version_id
      }
    );

    this.logChange(scheduleId, {
      action: 'rollback',
      version_id: rollbackVersion.version_id,
      rollback_to: targetVersionId,
      change_reason: reason,
      timestamp: new Date().toISOString()
    });

    return rollbackVersion;
  }

  // Get schedule statistics
  getScheduleStats(scheduleId) {
    const versions = this.scheduleVersions.get(scheduleId);
    const changeLog = this.changeLog.get(scheduleId);

    if (!versions) {
      throw new Error(`Schedule ${scheduleId} not found`);
    }

    const weatherTriggeredUpdates = versions.filter(v => v.metadata.weather_triggered).length;
    const manualUpdates = versions.filter(v => !v.metadata.weather_triggered).length;

    return {
      schedule_id: scheduleId,
      total_versions: versions.length,
      weather_triggered_updates: weatherTriggeredUpdates,
      manual_updates: manualUpdates,
      total_changes: changeLog ? changeLog.length : 0,
      created_at: versions[0]?.metadata.created_at,
      last_updated: versions[versions.length - 1]?.metadata.created_at,
      current_version: this.getCurrentVersion(scheduleId).version_id
    };
  }

  // Get all schedules summary
  getAllSchedulesSummary() {
    const schedules = [];
    
    for (const [scheduleId] of this.scheduleVersions) {
      try {
        const stats = this.getScheduleStats(scheduleId);
        const currentVersion = this.getCurrentVersion(scheduleId);
        
        schedules.push({
          schedule_id: scheduleId,
          current_version: currentVersion.version_id,
          status: currentVersion.status,
          ...stats
        });
      } catch (error) {
        console.error(`Error getting stats for schedule ${scheduleId}:`, error.message);
      }
    }

    return schedules;
  }

  // Clean up old versions (keep only recent ones)
  cleanupOldVersions(scheduleId, keepVersions = 10) {
    const versions = this.scheduleVersions.get(scheduleId);
    if (!versions || versions.length <= keepVersions) {
      return { cleaned: 0, remaining: versions ? versions.length : 0 };
    }

    // Sort by version number and keep the most recent ones
    versions.sort((a, b) => b.version_number - a.version_number);
    const toKeep = versions.slice(0, keepVersions);
    const toRemove = versions.slice(keepVersions);

    // Update storage
    this.scheduleVersions.set(scheduleId, toKeep);

    return {
      cleaned: toRemove.length,
      remaining: toKeep.length,
      removed_versions: toRemove.map(v => v.version_id)
    };
  }
}
