// Weather service using OpenWeatherMap API

import axios from 'axios';
import moment from 'moment';

export class WeatherService {
  constructor() {
    this.apiKey = process.env.OPENWEATHER_API_KEY;
    this.baseUrl = 'https://api.openweathermap.org/data/2.5';
    this.isConfigured = !!this.apiKey && this.apiKey !== 'your_openweather_api_key_here';
  }

  // Check if weather service is available
  isAvailable() {
    return this.isConfigured;
  }

  async getCurrentWeather(latitude, longitude) {
    // Return mock data if API key is not configured
    if (!this.isConfigured) {
      console.log('Weather API not configured, using mock data');
      return this.getMockWeatherData(latitude, longitude);
    }

    try {
      const response = await axios.get(`${this.baseUrl}/weather`, {
        params: {
          lat: latitude,
          lon: longitude,
          appid: this.apiKey,
          units: 'metric' // Use metric units (Celsius)
        }
      });

      const data = response.data;

      return this.formatWeatherData(data);
    } catch (error) {
      console.error('Weather API error:', error.message);
      // Return mock weather if API fails
      return this.getMockWeatherData(latitude, longitude);
    }
  }

  formatWeatherData(data) {
    return {
      conditions: data.weather[0].description,
      temperature: {
        value: Math.round(data.main.temp),
        unit: 'C',
        feels_like: Math.round(data.main.feels_like)
      },
      humidity: data.main.humidity,
      wind_speed: data.wind.speed,
      wind_direction: data.wind.deg,
      pressure: data.main.pressure,
      cloudiness: data.clouds.all,
      sunrise: moment.unix(data.sys.sunrise).format('HH:mm'),
      sunset: moment.unix(data.sys.sunset).format('HH:mm'),
      source: 'OpenWeatherMap'
    };
  }

  getMockWeatherData(latitude, longitude) {
    // Generate realistic mock weather based on location and season
    const season = this.getCurrentSeason(latitude);
    const baseTemp = this.getSeasonalTemperature(latitude, season);

    return {
      conditions: this.getMockConditions(season),
      temperature: {
        value: baseTemp + Math.floor(Math.random() * 6) - 3, // ±3°C variation
        unit: 'C',
        feels_like: baseTemp + Math.floor(Math.random() * 4) - 2
      },
      humidity: 50 + Math.floor(Math.random() * 30), // 50-80%
      wind_speed: 2 + Math.random() * 8, // 2-10 m/s
      wind_direction: Math.floor(Math.random() * 360),
      pressure: 1010 + Math.floor(Math.random() * 20), // 1010-1030 hPa
      cloudiness: Math.floor(Math.random() * 80), // 0-80%
      sunrise: '06:30',
      sunset: '18:30',
      source: 'Mock Data (Weather API not configured)'
    };
  }

  async getWeatherForecast(latitude, longitude, days = 5) {
    // Return mock forecast if API key is not configured
    if (!this.isConfigured) {
      console.log('Weather forecast API not configured, using mock data');
      return this.getMockForecastData(latitude, days);
    }

    try {
      const response = await axios.get(`${this.baseUrl}/forecast`, {
        params: {
          lat: latitude,
          lon: longitude,
          appid: this.apiKey,
          units: 'metric',
          cnt: days * 8 // 8 forecasts per day (3-hour intervals)
        }
      });

      const forecasts = response.data.list.map(item => ({
        datetime: moment.unix(item.dt).format(),
        temperature: Math.round(item.main.temp),
        conditions: item.weather[0].description,
        humidity: item.main.humidity,
        rain_probability: item.pop * 100,
        rain_volume: item.rain?.['3h'] || 0
      }));

      return forecasts;
    } catch (error) {
      console.error('Weather forecast error:', error.message);
      return this.getMockForecastData(latitude, days);
    }
  }

  getMockForecastData(latitude, days) {
    const forecasts = [];
    const season = this.getCurrentSeason(latitude);
    const baseTemp = this.getSeasonalTemperature(latitude, season);

    for (let i = 0; i < days; i++) {
      const date = moment().add(i, 'days');
      forecasts.push({
        datetime: date.format(),
        temperature: baseTemp + Math.floor(Math.random() * 8) - 4,
        conditions: this.getMockConditions(season),
        humidity: 40 + Math.floor(Math.random() * 40),
        rain_probability: Math.floor(Math.random() * 60),
        rain_volume: Math.random() * 5
      });
    }

    return forecasts;
  }

  getCurrentSeason(latitude) {
    const month = new Date().getMonth() + 1; // 1-12
    const isNorthern = latitude > 0;

    if (isNorthern) {
      if (month >= 3 && month <= 5) return 'spring';
      if (month >= 6 && month <= 8) return 'summer';
      if (month >= 9 && month <= 11) return 'autumn';
      return 'winter';
    } else {
      if (month >= 3 && month <= 5) return 'autumn';
      if (month >= 6 && month <= 8) return 'winter';
      if (month >= 9 && month <= 11) return 'spring';
      return 'summer';
    }
  }

  getSeasonalTemperature(latitude, season) {
    const absLat = Math.abs(latitude);
    let baseTemp = 25; // Default tropical temperature

    // Adjust for latitude (closer to poles = colder)
    if (absLat > 60) baseTemp = 5;
    else if (absLat > 45) baseTemp = 15;
    else if (absLat > 30) baseTemp = 20;

    // Adjust for season
    const seasonAdjustment = {
      'spring': 0,
      'summer': 8,
      'autumn': -3,
      'winter': -10
    };

    return baseTemp + (seasonAdjustment[season] || 0);
  }

  getMockConditions(season) {
    const conditions = {
      'spring': ['partly cloudy', 'light rain', 'clear sky', 'overcast'],
      'summer': ['clear sky', 'partly cloudy', 'hot', 'sunny'],
      'autumn': ['overcast', 'light rain', 'partly cloudy', 'foggy'],
      'winter': ['overcast', 'light rain', 'cloudy', 'cold']
    };

    const seasonConditions = conditions[season] || conditions['spring'];
    return seasonConditions[Math.floor(Math.random() * seasonConditions.length)];
  }

  // Get weather-based farming recommendations
  getWeatherPriority(weather) {
    const temp = weather.temperature.value;
    const humidity = typeof weather.humidity === 'string' ?
      parseInt(weather.humidity) : weather.humidity;

    // Determine priority based on conditions
    if (weather.conditions.includes('storm') || weather.conditions.includes('heavy rain')) {
      return 'avoid';
    } else if (temp > 35 || temp < 5 || humidity > 90) {
      return 'avoid';
    } else if (temp > 30 || temp < 10 || humidity > 80 || humidity < 30) {
      return 'caution';
    }

    return 'optimal';
  }

  getWeatherNote(weather, activity) {
    const conditions = [];

    if (weather.conditions && weather.conditions.includes('rain')) {
      conditions.push('Rain expected - plan indoor activities or drainage');
    }

    if (weather.temperature && weather.temperature.value > 30) {
      conditions.push('High temperature - work during cooler hours');
    }

    const humidity = typeof weather.humidity === 'string' ?
      parseInt(weather.humidity) : weather.humidity;
    if (humidity > 80) {
      conditions.push('High humidity - monitor for fungal diseases');
    }

    // Handle different wind object structures
    const windSpeed = weather.wind_speed ||
                     (weather.wind && weather.wind.speed) ||
                     0;
    if (windSpeed > 20) {
      conditions.push('Strong winds - avoid spraying operations');
    }

    return conditions.length > 0
      ? conditions.join('. ')
      : 'Favorable weather conditions for farming activities';
  }
}
