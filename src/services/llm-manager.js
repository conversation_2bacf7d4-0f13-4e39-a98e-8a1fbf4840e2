// LLM Manager for handling multiple AI providers (OpenAI and Claude)

import { OpenAIService } from './openai.js';
import { ClaudeService } from './claude.js';

export class LLMManager {
  constructor() {
    this.providers = {
      openai: new OpenAIService(),
      claude: new ClaudeService()
    };
    
    this.defaultProvider = process.env.DEFAULT_LLM_PROVIDER || 'claude';
    this.strictMode = process.env.STRICT_MODE === 'true';
    this.failOnMissingServices = process.env.FAIL_ON_MISSING_CORE_SERVICES === 'true';
  }

  // Get available providers
  getAvailableProviders() {
    const status = {};
    for (const [name, provider] of Object.entries(this.providers)) {
      status[name] = {
        available: provider.isAvailable(),
        configured: provider.isConfigured
      };
    }
    return status;
  }

  // Validate that at least one LLM provider is available
  validateCoreServices() {
    const availableProviders = Object.entries(this.providers)
      .filter(([name, provider]) => provider.isAvailable())
      .map(([name]) => name);

    if (availableProviders.length === 0) {
      const error = 'No LLM providers are configured. Please configure at least one of: OPENAI_API_KEY, CLAUDE_API_KEY';
      
      if (this.failOnMissingServices) {
        throw new Error(error);
      } else {
        console.warn(error);
        return false;
      }
    }

    return true;
  }

  // Get the appropriate provider for a user
  getProvider(userPreference = null) {
    // Use user preference if provided and available
    if (userPreference && this.providers[userPreference]?.isAvailable()) {
      return this.providers[userPreference];
    }

    // Fall back to default provider if available
    if (this.providers[this.defaultProvider]?.isAvailable()) {
      return this.providers[this.defaultProvider];
    }

    // Use any available provider
    for (const provider of Object.values(this.providers)) {
      if (provider.isAvailable()) {
        return provider;
      }
    }

    // No providers available
    if (this.strictMode) {
      throw new Error('No LLM providers are available');
    }

    return null;
  }

  // Get provider name for a given provider instance
  getProviderName(provider) {
    for (const [name, p] of Object.entries(this.providers)) {
      if (p === provider) {
        return name;
      }
    }
    return 'unknown';
  }

  // Wrapper methods that delegate to the appropriate provider
  async getLocationContext(locationData, addressInfo, userPreference = null) {
    const provider = this.getProvider(userPreference);
    if (!provider) {
      throw new Error('No LLM provider available for location context generation');
    }

    try {
      const result = await provider.getLocationContext(locationData, addressInfo);
      result.llm_provider = this.getProviderName(provider);
      return result;
    } catch (error) {
      console.error(`${this.getProviderName(provider)} location context error:`, error.message);
      throw error;
    }
  }

  async getCropContext(cropData, locationContext, userPreference = null) {
    const provider = this.getProvider(userPreference);
    if (!provider) {
      throw new Error('No LLM provider available for crop context generation');
    }

    try {
      const result = await provider.getCropContext(cropData, locationContext);
      result.llm_provider = this.getProviderName(provider);
      return result;
    } catch (error) {
      console.error(`${this.getProviderName(provider)} crop context error:`, error.message);
      throw error;
    }
  }

  async generateWeeklySchedule(cropContext, locationContext, weekData, userPreference = null) {
    const provider = this.getProvider(userPreference);
    if (!provider) {
      throw new Error('No LLM provider available for weekly schedule generation');
    }

    try {
      const result = await provider.generateWeeklySchedule(cropContext, locationContext, weekData);
      result.llm_provider = this.getProviderName(provider);
      return result;
    } catch (error) {
      console.error(`${this.getProviderName(provider)} weekly schedule error:`, error.message);
      throw error;
    }
  }

  async generateYouTubeVideos(cropContext, weeklyActivity, locationContext, userPreference = null) {
    const provider = this.getProvider(userPreference);
    if (!provider) {
      throw new Error('No LLM provider available for YouTube video generation');
    }

    try {
      const result = await provider.generateYouTubeVideos(cropContext, weeklyActivity, locationContext);
      result.llm_provider = this.getProviderName(provider);
      return result;
    } catch (error) {
      console.error(`${this.getProviderName(provider)} YouTube videos error:`, error.message);
      throw error;
    }
  }

  async generateWebSearchSuggestions(topic, cropType, location, userPreference = null) {
    const provider = this.getProvider(userPreference);
    if (!provider) {
      throw new Error('No LLM provider available for web search suggestions');
    }

    try {
      const result = await provider.generateWebSearchSuggestions(topic, cropType, location);
      result.llm_provider = this.getProviderName(provider);
      return result;
    } catch (error) {
      console.error(`${this.getProviderName(provider)} web search suggestions error:`, error.message);
      throw error;
    }
  }

  async generatePestDiseaseInfo(cropContext, locationContext, weatherData, userPreference = null) {
    const provider = this.getProvider(userPreference);
    if (!provider) {
      throw new Error('No LLM provider available for pest disease information');
    }

    try {
      const result = await provider.generatePestDiseaseInfo(cropContext, locationContext, weatherData);
      result.llm_provider = this.getProviderName(provider);
      return result;
    } catch (error) {
      console.error(`${this.getProviderName(provider)} pest disease info error:`, error.message);
      throw error;
    }
  }

  // Get system status including all providers
  getSystemStatus() {
    const providers = this.getAvailableProviders();
    const availableCount = Object.values(providers).filter(p => p.available).length;
    
    return {
      llm_providers: providers,
      default_provider: this.defaultProvider,
      available_providers: availableCount,
      strict_mode: this.strictMode,
      core_services_validated: availableCount > 0
    };
  }

  // Get provider comparison information for users
  getProviderInfo() {
    return {
      available_providers: {
        claude: {
          name: "Claude",
          description: "Advanced reasoning and analysis with web search",
          strengths: ["Complex scheduling", "Detailed analysis", "Superior reasoning", "Web search"],
          model: process.env.CLAUDE_MODEL || 'claude-3-5-sonnet-latest',
          web_search: true,
          cost_tier: "premium",
          available: this.providers.claude.isAvailable()
        },
        openai: {
          name: "OpenAI GPT-4",
          description: "Reliable and cost-effective with web search",
          strengths: ["Fast responses", "Cost effective", "Proven reliability", "Web search"],
          model: process.env.OPENAI_MODEL || 'gpt-4o-2024-08-06',
          web_search: true,
          cost_tier: "standard",
          available: this.providers.openai.isAvailable()
        }
      },
      default_provider: this.defaultProvider,
      recommendation: "Both providers offer excellent capabilities. Choose based on your preference for reasoning depth (Claude) vs cost efficiency (OpenAI)."
    };
  }
}
