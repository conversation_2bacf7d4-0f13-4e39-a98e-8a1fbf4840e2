// Weather monitoring service for detecting significant weather changes

import { WeatherService } from './weather.js';
import moment from 'moment';

export class WeatherMonitorService {
  constructor() {
    this.weatherService = new WeatherService();
    this.weatherHistory = new Map(); // Store weather history by location
    this.monitoringIntervals = new Map(); // Store active monitoring intervals
    
    // Default thresholds for weather change detection
    this.defaultThresholds = {
      temperature: {
        significant: 5, // °C change
        extreme: 10     // °C change
      },
      humidity: {
        significant: 15, // % change
        extreme: 25      // % change
      },
      rain_probability: {
        significant: 30, // % change
        extreme: 50      // % change
      },
      wind_speed: {
        significant: 5,  // m/s change
        extreme: 10      // m/s change
      },
      pressure: {
        significant: 10, // hPa change
        extreme: 20      // hPa change
      }
    };
    
    // Activity-specific sensitivity levels
    this.activitySensitivity = {
      'spraying': {
        wind_speed: { significant: 3, extreme: 5 },
        rain_probability: { significant: 20, extreme: 30 }
      },
      'planting': {
        rain_probability: { significant: 25, extreme: 40 },
        temperature: { significant: 3, extreme: 8 }
      },
      'harvesting': {
        rain_probability: { significant: 20, extreme: 35 },
        humidity: { significant: 10, extreme: 20 }
      },
      'irrigation': {
        rain_probability: { significant: 30, extreme: 50 },
        humidity: { significant: 20, extreme: 30 }
      },
      'fertilizing': {
        rain_probability: { significant: 25, extreme: 40 },
        wind_speed: { significant: 4, extreme: 7 }
      }
    };
  }

  // Generate location key for weather history storage
  getLocationKey(latitude, longitude) {
    return `${latitude.toFixed(4)},${longitude.toFixed(4)}`;
  }

  // Store weather data in history
  storeWeatherData(latitude, longitude, weatherData) {
    const locationKey = this.getLocationKey(latitude, longitude);
    
    if (!this.weatherHistory.has(locationKey)) {
      this.weatherHistory.set(locationKey, []);
    }
    
    const history = this.weatherHistory.get(locationKey);
    history.push({
      ...weatherData,
      stored_at: new Date().toISOString()
    });
    
    // Keep only last 24 hours of data (assuming hourly checks)
    const cutoffTime = moment().subtract(24, 'hours');
    this.weatherHistory.set(locationKey, 
      history.filter(entry => moment(entry.timestamp).isAfter(cutoffTime))
    );
  }

  // Get weather history for a location
  getWeatherHistory(latitude, longitude, hours = 24) {
    const locationKey = this.getLocationKey(latitude, longitude);
    const history = this.weatherHistory.get(locationKey) || [];
    
    const cutoffTime = moment().subtract(hours, 'hours');
    return history.filter(entry => moment(entry.timestamp).isAfter(cutoffTime));
  }

  // Detect weather changes between two weather readings
  detectWeatherChanges(previousWeather, currentWeather, activityType = null) {
    if (!previousWeather || !currentWeather) {
      return { hasChanges: false, changes: [] };
    }

    const changes = [];
    const thresholds = activityType && this.activitySensitivity[activityType] 
      ? { ...this.defaultThresholds, ...this.activitySensitivity[activityType] }
      : this.defaultThresholds;

    // Temperature change
    const tempDiff = Math.abs(currentWeather.temperature.value - previousWeather.temperature.value);
    if (tempDiff >= thresholds.temperature.extreme) {
      changes.push({
        parameter: 'temperature',
        severity: 'extreme',
        change: tempDiff,
        from: previousWeather.temperature.value,
        to: currentWeather.temperature.value,
        unit: '°C',
        impact: 'High impact on farming activities'
      });
    } else if (tempDiff >= thresholds.temperature.significant) {
      changes.push({
        parameter: 'temperature',
        severity: 'significant',
        change: tempDiff,
        from: previousWeather.temperature.value,
        to: currentWeather.temperature.value,
        unit: '°C',
        impact: 'Moderate impact on farming activities'
      });
    }

    // Humidity change
    const humidityDiff = Math.abs(currentWeather.humidity - previousWeather.humidity);
    if (humidityDiff >= thresholds.humidity.extreme) {
      changes.push({
        parameter: 'humidity',
        severity: 'extreme',
        change: humidityDiff,
        from: previousWeather.humidity,
        to: currentWeather.humidity,
        unit: '%',
        impact: 'High impact on disease risk and plant stress'
      });
    } else if (humidityDiff >= thresholds.humidity.significant) {
      changes.push({
        parameter: 'humidity',
        severity: 'significant',
        change: humidityDiff,
        from: previousWeather.humidity,
        to: currentWeather.humidity,
        unit: '%',
        impact: 'Moderate impact on disease risk'
      });
    }

    // Wind speed change
    const windDiff = Math.abs(currentWeather.wind_speed - previousWeather.wind_speed);
    if (windDiff >= thresholds.wind_speed.extreme) {
      changes.push({
        parameter: 'wind_speed',
        severity: 'extreme',
        change: windDiff,
        from: previousWeather.wind_speed,
        to: currentWeather.wind_speed,
        unit: 'm/s',
        impact: 'High impact on spraying and delicate operations'
      });
    } else if (windDiff >= thresholds.wind_speed.significant) {
      changes.push({
        parameter: 'wind_speed',
        severity: 'significant',
        change: windDiff,
        from: previousWeather.wind_speed,
        to: currentWeather.wind_speed,
        unit: 'm/s',
        impact: 'Moderate impact on outdoor activities'
      });
    }

    // Pressure change
    const pressureDiff = Math.abs(currentWeather.pressure - previousWeather.pressure);
    if (pressureDiff >= thresholds.pressure.extreme) {
      changes.push({
        parameter: 'pressure',
        severity: 'extreme',
        change: pressureDiff,
        from: previousWeather.pressure,
        to: currentWeather.pressure,
        unit: 'hPa',
        impact: 'Indicates major weather system change'
      });
    } else if (pressureDiff >= thresholds.pressure.significant) {
      changes.push({
        parameter: 'pressure',
        severity: 'significant',
        change: pressureDiff,
        from: previousWeather.pressure,
        to: currentWeather.pressure,
        unit: 'hPa',
        impact: 'Weather pattern change likely'
      });
    }

    // Condition changes (qualitative)
    if (previousWeather.conditions !== currentWeather.conditions) {
      const severity = this.assessConditionChangeSeverity(
        previousWeather.conditions, 
        currentWeather.conditions
      );
      
      if (severity !== 'minor') {
        changes.push({
          parameter: 'conditions',
          severity: severity,
          from: previousWeather.conditions,
          to: currentWeather.conditions,
          impact: this.getConditionChangeImpact(previousWeather.conditions, currentWeather.conditions)
        });
      }
    }

    return {
      hasChanges: changes.length > 0,
      changes: changes,
      timestamp: new Date().toISOString(),
      location: currentWeather.location,
      activity_type: activityType
    };
  }

  // Assess severity of weather condition changes
  assessConditionChangeSeverity(fromCondition, toCondition) {
    const severeConditions = ['storm', 'heavy rain', 'thunderstorm', 'hail', 'snow'];
    const moderateConditions = ['rain', 'drizzle', 'fog', 'mist'];
    
    const fromSevere = severeConditions.some(cond => fromCondition.toLowerCase().includes(cond));
    const toSevere = severeConditions.some(cond => toCondition.toLowerCase().includes(cond));
    const fromModerate = moderateConditions.some(cond => fromCondition.toLowerCase().includes(cond));
    const toModerate = moderateConditions.some(cond => toCondition.toLowerCase().includes(cond));
    
    if (fromSevere || toSevere) return 'extreme';
    if (fromModerate || toModerate) return 'significant';
    return 'minor';
  }

  // Get impact description for condition changes
  getConditionChangeImpact(fromCondition, toCondition) {
    const impacts = {
      'to_rain': 'May require rescheduling outdoor activities',
      'to_storm': 'Immediate halt of all outdoor farming activities required',
      'to_clear': 'Good opportunity for weather-sensitive activities',
      'to_cloudy': 'Reduced solar radiation may affect plant growth'
    };
    
    if (toCondition.toLowerCase().includes('storm') || toCondition.toLowerCase().includes('heavy')) {
      return impacts.to_storm;
    } else if (toCondition.toLowerCase().includes('rain')) {
      return impacts.to_rain;
    } else if (toCondition.toLowerCase().includes('clear') || toCondition.toLowerCase().includes('sunny')) {
      return impacts.to_clear;
    } else if (toCondition.toLowerCase().includes('cloud')) {
      return impacts.to_cloudy;
    }
    
    return 'Weather condition change may affect planned activities';
  }

  // Check for weather changes at a specific location
  async checkWeatherChanges(latitude, longitude, activityType = null) {
    try {
      const currentWeather = await this.weatherService.getCurrentWeather(latitude, longitude);
      const history = this.getWeatherHistory(latitude, longitude, 6); // Last 6 hours
      
      // Store current weather
      this.storeWeatherData(latitude, longitude, currentWeather);
      
      if (history.length === 0) {
        return {
          hasChanges: false,
          message: 'No previous weather data for comparison',
          current_weather: currentWeather
        };
      }
      
      // Compare with most recent previous reading
      const previousWeather = history[history.length - 1];
      const changeDetection = this.detectWeatherChanges(previousWeather, currentWeather, activityType);
      
      return {
        ...changeDetection,
        current_weather: currentWeather,
        previous_weather: previousWeather,
        comparison_time_diff: moment(currentWeather.timestamp).diff(moment(previousWeather.timestamp), 'minutes')
      };
      
    } catch (error) {
      console.error('Weather change detection error:', error.message);
      throw error;
    }
  }

  // Get weather monitoring status for a location
  getMonitoringStatus(latitude, longitude) {
    const locationKey = this.getLocationKey(latitude, longitude);
    const history = this.weatherHistory.get(locationKey) || [];
    const isMonitoring = this.monitoringIntervals.has(locationKey);
    
    return {
      location: { latitude, longitude },
      is_monitoring: isMonitoring,
      history_entries: history.length,
      last_check: history.length > 0 ? history[history.length - 1].timestamp : null,
      monitoring_duration: isMonitoring ? 'Active' : 'Inactive'
    };
  }
}
