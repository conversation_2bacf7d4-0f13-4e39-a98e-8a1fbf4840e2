// Main orchestration service

import { GeocodingService } from './geocoding.js';
import { WeatherService } from './weather.js';
import { LLMManager } from './llm-manager.js';
import { YouTubeService } from './youtube.js';
import { ImageSearchService } from './image-search.js';
import moment from 'moment';

export class FarmSchedulerService {
  constructor() {
    this.geocodingService = new GeocodingService();
    this.weatherService = new WeatherService();
    this.llmManager = new LLMManager();
    this.youtubeService = new YouTubeService();
    this.imageSearchService = new ImageSearchService();

    // Validate core services on startup
    this.llmManager.validateCoreServices();
  }

  async processUserRegistration(phoneNumber, latitude, longitude, userPreferences = {}) {
    // Step 1: Reverse geocode to get address
    const addressInfo = await this.geocodingService.reverseGeocode(latitude, longitude);

    // Step 2: Get current weather
    const currentWeather = await this.weatherService.getCurrentWeather(latitude, longitude);

    // Step 3: Get location context from AI using user's preferred LLM
    const locationContext = await this.llmManager.getLocationContext(
      { latitude, longitude },
      addressInfo,
      userPreferences.llm_provider
    );

    // Store user profile (in real app, save to database)
    const userProfile = {
      phoneNumber,
      location: {
        latitude,
        longitude,
        ...addressInfo
      },
      currentWeather,
      locationContext,
      preferences: {
        llm_provider: userPreferences.llm_provider || this.llmManager.defaultProvider,
        ...userPreferences
      }
    };

    return userProfile;
  }

  async getCropContext(userProfile, cropType, plantingDate) {
    const cropContext = await this.llmManager.getCropContext(
      { cropType, plantingDate },
      userProfile.locationContext,
      userProfile.preferences?.llm_provider
    );

    return cropContext;
  }

  calculateWeekData(cropContext, targetDate = new Date()) {
    const plantingDate = moment(cropContext.crop_info.planting_date);
    const currentDate = moment(targetDate);
    const weeksSincePlanting = currentDate.diff(plantingDate, 'weeks');
    
    const weekStart = moment(targetDate).startOf('week');
    const weekEnd = moment(targetDate).endOf('week');
    
    return {
      weekNumber: weeksSincePlanting + 1,
      dateRange: `${weekStart.format('MMMM D')}-${weekEnd.format('D, YYYY')}`,
      year: currentDate.year()
    };
  }

  async generateWeeklySchedule(userProfile, cropContext, previousActivity = null) {
    const weekData = this.calculateWeekData(cropContext);
    
    // Get current weather for the location
    const currentWeather = await this.weatherService.getCurrentWeather(
      userProfile.location.latitude,
      userProfile.location.longitude
    );
    
    // Get weather forecast
    const forecast = await this.weatherService.getWeatherForecast(
      userProfile.location.latitude,
      userProfile.location.longitude,
      7 // 7 days forecast
    );
    
    const schedule = await this.llmManager.generateWeeklySchedule(
      cropContext,
      userProfile.locationContext,
      {
        ...weekData,
        previousActivity,
        currentWeather,
        forecast
      },
      userProfile.preferences?.llm_provider
    );

    // Generate YouTube video recommendations from AI
    const youtubeContent = await this.llmManager.generateYouTubeVideos(
      cropContext,
      schedule.activity,
      userProfile.locationContext,
      userProfile.preferences?.llm_provider
    );

    // Fetch actual YouTube videos based on AI recommendations
    let actualVideos = [];
    let videoSearchUrls = [];

    if (this.youtubeService.isAvailable()) {
      actualVideos = await this.youtubeService.searchFarmingVideos(
        cropContext.crop_info.name,
        schedule.activity.title,
        cropContext.crop_info.current_stage
      );
    } else {
      // Generate search URLs when YouTube API is not available
      videoSearchUrls = this.youtubeService.generateSearchUrls(
        cropContext.crop_info.name,
        schedule.activity.title,
        cropContext.crop_info.current_stage
      );
    }

    // Generate pest and disease information from AI
    const pestDiseaseInfo = await this.llmManager.generatePestDiseaseInfo(
      cropContext,
      userProfile.locationContext,
      currentWeather,
      userProfile.preferences?.llm_provider
    );

    // Fetch actual pest/disease images or generate search suggestions
    const pestImages = [];
    const imageSearchSuggestions = [];

    if (pestDiseaseInfo.alerts && pestDiseaseInfo.alerts.length > 0) {
      for (const alert of pestDiseaseInfo.alerts.slice(0, 2)) {
        if (this.imageSearchService.isAvailable()) {
          const images = await this.imageSearchService.searchPestDiseaseImages(
            alert.name,
            cropContext.crop_info.name,
            alert.symptoms?.early_stage || []
          );

          pestImages.push({
            pest_name: alert.name,
            images: this.imageSearchService.formatForGallery(images)
          });
        } else {
          // Generate search suggestions when image APIs are not available
          const suggestions = this.imageSearchService.generateSearchSuggestions(
            alert.name,
            cropContext.crop_info.name,
            alert.symptoms?.early_stage || []
          );

          imageSearchSuggestions.push({
            pest_name: alert.name,
            ...suggestions
          });
        }
      }
    }

    // Enhance schedule with weather data
    const weatherPriority = this.weatherService.getWeatherPriority(currentWeather);
    const weatherNote = this.weatherService.getWeatherNote(currentWeather, schedule.activity.title);
    
    // Add full schedule structure with all components
    const fullSchedule = {
      ...schedule,
      weather: {
        ...currentWeather,
        priority: weatherPriority,
        weather_note: weatherNote
      },
      weather_considerations: schedule.weather_considerations || {
        optimal: { conditions: "", details: "" },
        avoid: { conditions: "", details: "" },
        modifications: { conditions: "", details: "" }
      },
      step_by_step_instructions: schedule.step_by_step_instructions || [],
      emergency_protocols: schedule.emergency_protocols || [],
      required_resources: schedule.required_resources || [],
      help_suggestions: schedule.help_suggestions || [],
      learning_resources: {
        videos: actualVideos.length > 0 ? actualVideos : youtubeContent.videos || [],
        video_recommendations: youtubeContent.videos || [],
        video_search_urls: videoSearchUrls,
        articles: schedule.learning_resources?.articles || [],
        web_search_suggestions: await this.llmManager.generateWebSearchSuggestions(
          schedule.activity.title,
          cropContext.crop_info.name,
          userProfile.locationContext.location.city,
          userProfile.preferences?.llm_provider
        )
      },
      pest_disease_alerts: pestDiseaseInfo.alerts || [],
      pest_disease_images: pestImages,
      image_search_suggestions: imageSearchSuggestions
    };

    return fullSchedule;
  }

  // Generate multiple weeks schedule
  async generateMultiWeekSchedule(userProfile, cropContext, numberOfWeeks = 4) {
    const schedules = [];
    let previousActivity = null;

    for (let i = 0; i < numberOfWeeks; i++) {
      const targetDate = moment().add(i, 'weeks').toDate();
      const schedule = await this.generateWeeklySchedule(
        userProfile, 
        cropContext, 
        previousActivity
      );
      
      schedules.push(schedule);
      previousActivity = schedule.activity.title;
    }

    return schedules;
  }

  // Get system status including all services
  getSystemStatus() {
    const llmStatus = this.llmManager.getSystemStatus();

    return {
      ...llmStatus,
      services: {
        geocoding: {
          name: "OpenStreetMap Geocoding",
          status: "healthy", // Always available
          description: "Address lookup and reverse geocoding"
        },
        weather: {
          name: "Weather Service",
          status: this.weatherService.isAvailable() ? "healthy" : "unavailable",
          description: "Current weather and forecasts"
        },
        youtube: {
          name: "YouTube Search",
          status: this.youtubeService.isAvailable() ? "healthy" : "unavailable",
          description: "Educational video recommendations",
          optional: true
        },
        image_search: {
          name: "Image Search",
          status: this.imageSearchService.isAvailable() ? "healthy" : "unavailable",
          description: "Pest and disease image identification",
          optional: true
        }
      }
    };
  }

  // Get provider information for users
  getProviderInfo() {
    return this.llmManager.getProviderInfo();
  }
}
