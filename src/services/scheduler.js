// Main orchestration service

import { GeocodingService } from './geocoding.js';
import { WeatherService } from './weather.js';
import { LLMManager } from './llm-manager.js';
import { YouTubeService } from './youtube.js';
import { ImageSearchService } from './image-search.js';
import { ConfigService } from './config.js';
import moment from 'moment';

export class FarmSchedulerService {
  constructor() {
    this.configService = new ConfigService();
    this.geocodingService = new GeocodingService();
    this.weatherService = new WeatherService();
    this.llmManager = new LLMManager();
    this.youtubeService = new YouTubeService();
    this.imageSearchService = new ImageSearchService();

    // Validate core services on startup
    this.validateSystemConfiguration();
  }

  // Validate system configuration on startup
  validateSystemConfiguration() {
    const validation = this.configService.validateCoreServices();

    if (!validation.valid) {
      const errorMessage = `System configuration errors:\n${validation.errors.join('\n')}`;

      if (this.configService.get('FAIL_ON_MISSING_CORE_SERVICES')) {
        throw new Error(errorMessage);
      } else {
        console.warn(errorMessage);
      }
    }

    if (validation.warnings.length > 0) {
      console.warn(`Configuration warnings:\n${validation.warnings.join('\n')}`);
    }

    console.log('System configuration validated successfully');
  }

  async processUserRegistration(phoneNumber, latitude, longitude, userPreferences = {}) {
    // Step 1: Reverse geocode to get address
    const addressInfo = await this.geocodingService.reverseGeocode(latitude, longitude);

    // Step 2: Get current weather
    const currentWeather = await this.weatherService.getCurrentWeather(latitude, longitude);

    // Step 3: Get location context from AI using user's preferred LLM
    const locationContext = await this.llmManager.getLocationContext(
      { latitude, longitude },
      addressInfo,
      userPreferences.llm_provider
    );

    // Store user profile (in real app, save to database)
    const userProfile = {
      phoneNumber,
      location: {
        latitude,
        longitude,
        ...addressInfo
      },
      currentWeather,
      locationContext,
      preferences: {
        llm_provider: userPreferences.llm_provider || this.llmManager.defaultProvider,
        ...userPreferences
      }
    };

    return userProfile;
  }

  async getCropContext(userProfile, cropType, plantingDate) {
    const cropContext = await this.llmManager.getCropContext(
      { cropType, plantingDate },
      userProfile.locationContext,
      userProfile.preferences?.llm_provider
    );

    return cropContext;
  }

  calculateWeekData(cropContext, targetDate = new Date()) {
    const plantingDate = moment(cropContext.crop_info.planting_date || cropContext.crop_info.planting_date);
    const currentDate = moment(targetDate);
    const weeksSincePlanting = currentDate.diff(plantingDate, 'weeks');

    const weekStart = moment(targetDate).startOf('week');
    const weekEnd = moment(targetDate).endOf('week');

    return {
      weekNumber: weeksSincePlanting + 1,
      dateRange: `${weekStart.format('MMMM D')}-${weekEnd.format('D, YYYY')}`,
      year: currentDate.year(),
      targetDate: currentDate.format('YYYY-MM-DD'),
      weekStart: weekStart.format('YYYY-MM-DD'),
      weekEnd: weekEnd.format('YYYY-MM-DD'),
      daysSincePlanting: currentDate.diff(plantingDate, 'days'),
      isHistorical: currentDate.isBefore(moment(), 'day'),
      isFuture: currentDate.isAfter(moment(), 'day')
    };
  }

  // Enhanced date validation for agricultural context
  validateTargetDate(targetDate, cropContext) {
    const date = moment(targetDate);
    const plantingDate = moment(cropContext.crop_info.planting_date || cropContext.crop_info.planting_date);
    const harvestDate = moment(cropContext.crop_info.expected_harvest_date);
    const now = moment();

    const errors = [];
    const warnings = [];

    // Basic date validation
    if (!date.isValid()) {
      errors.push('Invalid date format. Please use YYYY-MM-DD, ISO string, or valid date format.');
      return { valid: false, errors, warnings };
    }

    // Check if date is too far in the past (more than 2 years)
    if (date.isBefore(now.clone().subtract(2, 'years'))) {
      errors.push('Target date is too far in the past (more than 2 years ago).');
    }

    // Check if date is too far in the future (more than 2 years)
    if (date.isAfter(now.clone().add(2, 'years'))) {
      errors.push('Target date is too far in the future (more than 2 years from now).');
    }

    // Check if date is before planting date
    if (plantingDate.isValid() && date.isBefore(plantingDate)) {
      warnings.push(`Target date is before planting date (${plantingDate.format('YYYY-MM-DD')}). Schedule will be theoretical.`);
    }

    // Check if date is after expected harvest
    if (harvestDate && harvestDate.isValid() && date.isAfter(harvestDate)) {
      warnings.push(`Target date is after expected harvest date (${harvestDate.format('YYYY-MM-DD')}). Crop may have been harvested.`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      dateInfo: {
        formatted: date.format('YYYY-MM-DD'),
        daysSincePlanting: plantingDate.isValid() ? date.diff(plantingDate, 'days') : null,
        daysToHarvest: harvestDate && harvestDate.isValid() ? harvestDate.diff(date, 'days') : null,
        isHistorical: date.isBefore(now, 'day'),
        isFuture: date.isAfter(now, 'day'),
        season: this.getSeasonForDate(date, cropContext.location?.latitude || 0)
      }
    };
  }

  // Helper method to determine season for a given date and latitude
  getSeasonForDate(date, latitude) {
    const month = date.month() + 1; // moment months are 0-based
    const isNorthern = latitude > 0;

    if (Math.abs(latitude) < 23.5) {
      // Tropical regions
      return month >= 6 && month <= 9 ? 'Monsoon' : 'Dry Season';
    }

    if (isNorthern) {
      if (month >= 3 && month <= 5) return 'Spring';
      if (month >= 6 && month <= 8) return 'Summer';
      if (month >= 9 && month <= 11) return 'Autumn';
      return 'Winter';
    } else {
      // Southern hemisphere - seasons are opposite
      if (month >= 3 && month <= 5) return 'Autumn';
      if (month >= 6 && month <= 8) return 'Winter';
      if (month >= 9 && month <= 11) return 'Spring';
      return 'Summer';
    }
  }

  async generateWeeklySchedule(userProfile, cropContext, previousActivity = null, targetDate = null) {
    // Use provided target date or default to current date
    const scheduleDate = targetDate ? new Date(targetDate) : new Date();

    // Validate target date if provided
    if (targetDate) {
      const validation = this.validateTargetDate(targetDate, cropContext);
      if (!validation.valid) {
        throw new Error(`Invalid target date: ${validation.errors.join(', ')}`);
      }

      // Log warnings if any
      if (validation.warnings.length > 0) {
        console.warn(`Date validation warnings: ${validation.warnings.join(', ')}`);
      }
    }

    const weekData = this.calculateWeekData(cropContext, scheduleDate);

    // Get weather for the target date (current weather if today, forecast if future, historical if past)
    let currentWeather, forecast;

    if (weekData.isHistorical) {
      // For historical dates, we can't get actual weather, so use mock data or skip
      currentWeather = {
        temperature: 20,
        humidity: 60,
        conditions: 'Historical data not available',
        note: 'Weather data not available for historical dates'
      };
      forecast = [];
    } else {
      // Get current weather for the location
      currentWeather = await this.weatherService.getCurrentWeather(
        userProfile.location.latitude,
        userProfile.location.longitude
      );

      // Get weather forecast
      forecast = await this.weatherService.getWeatherForecast(
        userProfile.location.latitude,
        userProfile.location.longitude,
        7 // 7 days forecast
      );
    }
    
    const schedule = await this.llmManager.generateWeeklySchedule(
      cropContext,
      userProfile.locationContext,
      {
        ...weekData,
        previousActivity,
        currentWeather,
        forecast
      },
      userProfile.preferences?.llm_provider
    );

    // Generate YouTube video recommendations from AI
    const youtubeContent = await this.llmManager.generateYouTubeVideos(
      cropContext,
      schedule.activity,
      userProfile.locationContext,
      userProfile.preferences?.llm_provider
    );

    // Fetch actual YouTube videos based on AI recommendations
    let actualVideos = [];
    let videoSearchUrls = [];

    if (this.youtubeService.isAvailable()) {
      actualVideos = await this.youtubeService.searchFarmingVideos(
        cropContext.crop_info.name,
        schedule.activity.title,
        cropContext.crop_info.current_stage
      );
    } else {
      // Generate search URLs when YouTube API is not available
      videoSearchUrls = this.youtubeService.generateSearchUrls(
        cropContext.crop_info.name,
        schedule.activity.title,
        cropContext.crop_info.current_stage
      );
    }

    // Generate pest and disease information from AI
    const pestDiseaseInfo = await this.llmManager.generatePestDiseaseInfo(
      cropContext,
      userProfile.locationContext,
      currentWeather,
      userProfile.preferences?.llm_provider
    );

    // Fetch actual pest/disease images or generate search suggestions
    const pestImages = [];
    const imageSearchSuggestions = [];

    if (pestDiseaseInfo.alerts && pestDiseaseInfo.alerts.length > 0) {
      for (const alert of pestDiseaseInfo.alerts.slice(0, 2)) {
        if (this.imageSearchService.isAvailable()) {
          const images = await this.imageSearchService.searchPestDiseaseImages(
            alert.name,
            cropContext.crop_info.name,
            alert.symptoms?.early_stage || []
          );

          pestImages.push({
            pest_name: alert.name,
            images: this.imageSearchService.formatForGallery(images)
          });
        } else {
          // Generate search suggestions when image APIs are not available
          const suggestions = this.imageSearchService.generateSearchSuggestions(
            alert.name,
            cropContext.crop_info.name,
            alert.symptoms?.early_stage || []
          );

          imageSearchSuggestions.push({
            pest_name: alert.name,
            ...suggestions
          });
        }
      }
    }

    // Enhance schedule with weather data
    const weatherPriority = this.weatherService.getWeatherPriority(currentWeather);
    const weatherNote = this.weatherService.getWeatherNote(currentWeather, schedule.activity.title);
    
    // Add full schedule structure with all components
    const fullSchedule = {
      ...schedule,
      weather: {
        ...currentWeather,
        priority: weatherPriority,
        weather_note: weatherNote
      },
      weather_considerations: schedule.weather_considerations || {
        optimal: { conditions: "", details: "" },
        avoid: { conditions: "", details: "" },
        modifications: { conditions: "", details: "" }
      },
      step_by_step_instructions: schedule.step_by_step_instructions || [],
      emergency_protocols: schedule.emergency_protocols || [],
      required_resources: schedule.required_resources || [],
      help_suggestions: schedule.help_suggestions || [],
      learning_resources: {
        videos: actualVideos.length > 0 ? actualVideos : youtubeContent.videos || [],
        video_recommendations: youtubeContent.videos || [],
        video_search_urls: videoSearchUrls,
        articles: schedule.learning_resources?.articles || [],
        web_search_suggestions: await this.llmManager.generateWebSearchSuggestions(
          schedule.activity.title,
          cropContext.crop_info.name,
          userProfile.locationContext.location.city,
          userProfile.preferences?.llm_provider
        )
      },
      pest_disease_alerts: pestDiseaseInfo.alerts || [],
      pest_disease_images: pestImages,
      image_search_suggestions: imageSearchSuggestions
    };

    return fullSchedule;
  }

  // Generate multiple weeks schedule with optional start date
  async generateMultiWeekSchedule(userProfile, cropContext, numberOfWeeks = 4, startDate = null) {
    const schedules = [];
    let previousActivity = null;

    // Use provided start date or default to current date
    const baseDate = startDate ? moment(startDate) : moment();

    // Validate start date if provided
    if (startDate) {
      const validation = this.validateTargetDate(startDate, cropContext);
      if (!validation.valid) {
        throw new Error(`Invalid start date: ${validation.errors.join(', ')}`);
      }
    }

    for (let i = 0; i < numberOfWeeks; i++) {
      const targetDate = baseDate.clone().add(i, 'weeks').toDate();
      const schedule = await this.generateWeeklySchedule(
        userProfile,
        cropContext,
        previousActivity,
        targetDate
      );

      schedules.push(schedule);
      previousActivity = schedule.activity.title;
    }

    return {
      schedules,
      summary: {
        total_weeks: numberOfWeeks,
        start_date: baseDate.format('YYYY-MM-DD'),
        end_date: baseDate.clone().add(numberOfWeeks - 1, 'weeks').format('YYYY-MM-DD'),
        date_range: `${baseDate.format('MMM D, YYYY')} - ${baseDate.clone().add(numberOfWeeks - 1, 'weeks').format('MMM D, YYYY')}`,
        includes_historical: baseDate.isBefore(moment(), 'day'),
        includes_future: baseDate.clone().add(numberOfWeeks - 1, 'weeks').isAfter(moment(), 'day')
      }
    };
  }

  // Generate schedule for a specific date range (non-contiguous dates)
  async generateDateRangeSchedule(userProfile, cropContext, dateList) {
    if (!Array.isArray(dateList) || dateList.length === 0) {
      throw new Error('Date list must be a non-empty array');
    }

    const schedules = [];
    const errors = [];
    let previousActivity = null;

    // Sort dates chronologically
    const sortedDates = dateList
      .map(date => ({ original: date, moment: moment(date) }))
      .sort((a, b) => a.moment.diff(b.moment))
      .map(item => item.original);

    for (const targetDate of sortedDates) {
      try {
        const schedule = await this.generateWeeklySchedule(
          userProfile,
          cropContext,
          previousActivity,
          targetDate
        );

        schedules.push(schedule);
        previousActivity = schedule.activity.title;
      } catch (error) {
        errors.push({
          date: targetDate,
          error: error.message
        });
      }
    }

    return {
      schedules,
      errors,
      summary: {
        total_requested: dateList.length,
        successful: schedules.length,
        failed: errors.length,
        date_range: schedules.length > 0 ? {
          start: schedules[0].week_info.weekStart,
          end: schedules[schedules.length - 1].week_info.weekEnd
        } : null
      }
    };
  }

  // Get system status including all services
  getSystemStatus() {
    const llmStatus = this.llmManager.getSystemStatus();
    const configSummary = this.configService.getConfigSummary();

    return {
      ...llmStatus,
      configuration: {
        strict_mode: configSummary.strict_mode,
        fail_on_missing_services: configSummary.fail_on_missing_services,
        environment: configSummary.environment,
        validation: configSummary.validation
      },
      services: {
        geocoding: {
          name: "OpenStreetMap Geocoding",
          status: this.configService.isServiceAvailable('GEOCODING') ? "healthy" : "unavailable",
          enabled: this.configService.isServiceEnabled('GEOCODING'),
          configured: this.configService.isServiceConfigured('GEOCODING'),
          description: "Address lookup and reverse geocoding"
        },
        weather: {
          name: "Weather Service",
          status: this.configService.isServiceAvailable('WEATHER') ? "healthy" : "unavailable",
          enabled: this.configService.isServiceEnabled('WEATHER'),
          configured: this.configService.isServiceConfigured('WEATHER'),
          description: "Current weather and forecasts"
        },
        youtube: {
          name: "YouTube Search",
          status: this.configService.isServiceAvailable('YOUTUBE') ? "healthy" : "unavailable",
          enabled: this.configService.isServiceEnabled('YOUTUBE'),
          configured: this.configService.isServiceConfigured('YOUTUBE'),
          description: "Educational video recommendations",
          optional: true
        },
        image_search: {
          name: "Image Search",
          status: this.configService.isServiceAvailable('IMAGE_SEARCH') ? "healthy" : "unavailable",
          enabled: this.configService.isServiceEnabled('IMAGE_SEARCH'),
          configured: this.configService.isServiceConfigured('IMAGE_SEARCH'),
          description: "Pest and disease image identification",
          optional: true
        }
      },
      setup_instructions: configSummary.validation.valid ? null : this.configService.getSetupInstructions()
    };
  }

  // Get provider information for users
  getProviderInfo() {
    return this.llmManager.getProviderInfo();
  }
}
