// Claude service for multi-step farm schedule generation with web search

import Anthropic from '@anthropic-ai/sdk';
import { PROMPTS } from '../prompts/templates.js';
import { 
  LocationContextSchema, 
  CropContextSchema, 
  WeeklyScheduleSchema,
  YouTubeVideosSchema,
  PestDiseaseSchema
} from '../models/schemas.js';

export class ClaudeService {
  constructor() {
    this.apiKey = process.env.CLAUDE_API_KEY;
    this.isConfigured = !!this.apiKey && this.apiKey !== 'your_claude_api_key_here';

    if (this.isConfigured) {
      this.client = new Anthropic({
        apiKey: this.apiKey
      });
      this.model = process.env.CLAUDE_MODEL || 'claude-3-5-sonnet-latest';
    } else {
      console.warn('Claude API key not configured - AI features will be limited');
    }
  }

  // Check if Claude is available
  isAvailable() {
    return this.isConfigured;
  }

  // Helper method to create web search tool configuration
  getWebSearchTool(maxUses = 3, userLocation = null) {
    const tool = {
      type: "web_search_20250305",
      name: "web_search",
      max_uses: maxUses
    };

    // Add agricultural domain filtering
    tool.allowed_domains = [
      "extension.org",
      "usda.gov", 
      "fao.org",
      "weather.gov",
      "farmers.gov",
      "agriculture.com",
      "cropscience.org.au",
      "agric.wa.gov.au"
    ];

    // Add user location if provided
    if (userLocation) {
      tool.user_location = {
        type: "approximate",
        city: userLocation.city,
        region: userLocation.region || userLocation.state,
        country: this.getCountryCode(userLocation.country),
        timezone: userLocation.timezone || "UTC"
      };
    }

    return tool;
  }

  // Helper method to make Claude API calls with structured output
  async makeStructuredCall(prompt, systemPrompt, schema, useWebSearch = false, userLocation = null) {
    if (!this.isConfigured) {
      throw new Error('Claude API key not configured');
    }

    const messages = [
      {
        role: "user",
        content: prompt
      }
    ];

    const requestOptions = {
      model: this.model,
      max_tokens: 4000,
      system: systemPrompt,
      messages: messages
    };

    // Add web search tool if requested
    if (useWebSearch) {
      requestOptions.tools = [this.getWebSearchTool(3, userLocation)];
    }

    const response = await this.client.messages.create(requestOptions);
    
    // Extract the final text content (after any tool use)
    const textContent = response.content
      .filter(block => block.type === 'text')
      .map(block => block.text)
      .join('\n');

    // Try to parse as JSON - Claude should return structured data
    try {
      return JSON.parse(textContent);
    } catch (error) {
      console.error('Failed to parse Claude response as JSON:', error);
      throw new Error('Claude returned invalid JSON response');
    }
  }

  async getLocationContext(locationData, addressInfo) {
    if (!this.isConfigured) {
      throw new Error('Claude API key not configured');
    }

    const prompt = PROMPTS.LOCATION_CONTEXT
      .replace('{latitude}', locationData.latitude)
      .replace('{longitude}', locationData.longitude)
      .replace('{address}', addressInfo.address)
      .replace('{current_date}', new Date().toISOString());

    const systemPrompt = `You are an agricultural expert analyzing locations for farming. Use web search to find current, accurate information about the location's climate, soil conditions, and agricultural practices. Return your response as valid JSON matching the LocationContext schema.`;

    const userLocation = {
      city: addressInfo.city,
      region: addressInfo.state,
      country: addressInfo.country
    };

    return await this.makeStructuredCall(
      prompt, 
      systemPrompt, 
      LocationContextSchema, 
      true, // Use web search
      userLocation
    );
  }

  async getCropContext(cropData, locationContext) {
    if (!this.isConfigured) {
      throw new Error('Claude API key not configured');
    }

    const prompt = PROMPTS.CROP_CONTEXT
      .replace('{crop_type}', cropData.cropType)
      .replace('{planting_date}', cropData.plantingDate)
      .replace('{current_date}', new Date().toISOString())
      .replace('{location}', locationContext.location.city)
      .replace('{climate_zone}', locationContext.climate_zone.zone_name)
      .replace('{season}', locationContext.current_season.name);

    const systemPrompt = `You are an expert crop advisor. Use web search to find current best practices and specific information about the crop variety and growing conditions. Return your response as valid JSON matching the CropContext schema.`;

    const userLocation = {
      city: locationContext.location.city,
      region: locationContext.location.state,
      country: locationContext.location.country
    };

    return await this.makeStructuredCall(
      prompt, 
      systemPrompt, 
      CropContextSchema, 
      true, // Use web search
      userLocation
    );
  }

  async generateWeeklySchedule(cropContext, locationContext, weekData) {
    if (!this.isConfigured) {
      throw new Error('Claude API key not configured');
    }

    try {
      const prompt = PROMPTS.WEEKLY_SCHEDULE
        .replace('{crop_type}', cropContext.crop_info.name)
        .replace('{variety}', cropContext.crop_info.variety || 'standard')
        .replace('{crop_stage}', cropContext.crop_info.current_stage)
        .replace('{week_in_stage}', cropContext.stage_details.week_in_stage)
        .replace('{location}', locationContext.location.city)
        .replace('{climate_zone}', locationContext.climate_zone.zone_name)
        .replace('{season}', locationContext.current_season.name)
        .replace('{date_range}', weekData.dateRange)
        .replace('{previous_activity}', weekData.previousActivity || 'None');

      const systemPrompt = `You are an expert farm advisor creating detailed weekly schedules. Use web search to find current weather conditions, seasonal farming practices, and any recent agricultural advisories for the location. Include current weather data and forecasts in your recommendations. Return your response as valid JSON matching the WeeklySchedule schema.`;

      const userLocation = {
        city: locationContext.location.city,
        region: locationContext.location.state,
        country: locationContext.location.country
      };

      return await this.makeStructuredCall(
        prompt, 
        systemPrompt, 
        WeeklyScheduleSchema, 
        true, // Use web search for weather and current conditions
        userLocation
      );
    } catch (error) {
      console.error('Claude weekly schedule error:', error.message);
      throw error; // No fallback to mock data - fail properly
    }
  }

  async generateYouTubeVideos(cropContext, weeklyActivity, locationContext) {
    if (!this.isConfigured) {
      throw new Error('Claude API key not configured');
    }

    try {
      const prompt = PROMPTS.YOUTUBE_VIDEOS
        .replace('{crop_type}', cropContext.crop_info.name)
        .replace('{crop_stage}', cropContext.crop_info.current_stage)
        .replace('{activity_title}', weeklyActivity.title)
        .replace('{location}', locationContext.location.city)
        .replace('{language}', 'English'); // Can be made configurable

      const systemPrompt = `You are an agricultural education expert recommending practical YouTube videos. Use web search to find current, high-quality educational videos that match the farming activity and crop type. Return your response as valid JSON matching the YouTubeVideos schema.`;

      const userLocation = {
        city: locationContext.location.city,
        region: locationContext.location.state,
        country: locationContext.location.country
      };

      return await this.makeStructuredCall(
        prompt, 
        systemPrompt, 
        YouTubeVideosSchema, 
        true, // Use web search to find actual videos
        userLocation
      );
    } catch (error) {
      console.error('Claude YouTube videos error:', error.message);
      throw error; // No fallback to mock data
    }
  }

  // Generate web search suggestions using Claude's web search capabilities
  async generateWebSearchSuggestions(topic, cropType, location) {
    if (!this.isConfigured) {
      throw new Error('Claude API key not configured');
    }

    try {
      const prompt = `Generate 5 specific web search queries for farmers looking for information about "${topic}" related to ${cropType} farming in ${location}. Include both general and location-specific searches. Use web search to find current agricultural resources and trending topics. Return as JSON with array of search queries.`;

      const systemPrompt = `You are an expert at creating effective web search queries for agricultural information. Use web search to understand current agricultural trends and resources available for the specified topic and location.`;

      const userLocation = {
        city: location,
        country: "Unknown" // Will be refined based on location string
      };

      const response = await this.makeStructuredCall(
        prompt,
        systemPrompt,
        null, // No specific schema
        true, // Use web search
        userLocation
      );

      const queries = response.search_queries || response.queries || [];
      
      return {
        search_queries: queries,
        google_urls: queries.map(q =>
          `https://www.google.com/search?q=${encodeURIComponent(q)}`
        ),
        youtube_urls: queries.map(q =>
          `https://www.youtube.com/results?search_query=${encodeURIComponent(q)}`
        ),
        source: 'Claude Generated with Web Search'
      };
    } catch (error) {
      console.error('Claude web search generation error:', error.message);
      throw error; // No fallback to mock data
    }
  }

  async generatePestDiseaseInfo(cropContext, locationContext, weatherData) {
    if (!this.isConfigured) {
      throw new Error('Claude API key not configured');
    }

    try {
      const prompt = PROMPTS.PEST_DISEASE
        .replace('{crop_type}', cropContext.crop_info.name)
        .replace('{crop_stage}', cropContext.crop_info.current_stage)
        .replace('{location}', locationContext.location.city)
        .replace('{climate_zone}', locationContext.climate_zone.zone_name)
        .replace('{season}', locationContext.current_season.name)
        .replace('{weather_conditions}', JSON.stringify(weatherData));

      const systemPrompt = `You are an expert plant pathologist and entomologist. Use web search to find current pest and disease alerts, recent outbreaks, and updated management practices for the specified crop and location. Include current weather-based risk assessments. Return your response as valid JSON matching the PestDisease schema.`;

      const userLocation = {
        city: locationContext.location.city,
        region: locationContext.location.state,
        country: locationContext.location.country
      };

      return await this.makeStructuredCall(
        prompt, 
        systemPrompt, 
        PestDiseaseSchema, 
        true, // Use web search for current pest alerts
        userLocation
      );
    } catch (error) {
      console.error('Claude pest disease error:', error.message);
      throw error; // No fallback to mock data
    }
  }

  // Helper method to convert country name to 2-letter code
  getCountryCode(countryName) {
    if (!countryName) return 'US'; // Default fallback

    // Handle common country name variations
    const countryMap = {
      'United States': 'US',
      'United States of America': 'US',
      'USA': 'US',
      'Canada': 'CA',
      'United Kingdom': 'GB',
      'UK': 'GB',
      'Australia': 'AU',
      'India': 'IN',
      'China': 'CN',
      'Japan': 'JP',
      'Germany': 'DE',
      'France': 'FR',
      'Brazil': 'BR',
      'Mexico': 'MX',
      'Russia': 'RU',
      'South Africa': 'ZA',
      'Nigeria': 'NG',
      'Kenya': 'KE',
      'Egypt': 'EG',
      'Argentina': 'AR',
      'Chile': 'CL',
      'Peru': 'PE',
      'Colombia': 'CO',
      'Venezuela': 'VE',
      'Thailand': 'TH',
      'Vietnam': 'VN',
      'Philippines': 'PH',
      'Indonesia': 'ID',
      'Malaysia': 'MY',
      'Singapore': 'SG',
      'New Zealand': 'NZ',
      'Italy': 'IT',
      'Spain': 'ES',
      'Netherlands': 'NL',
      'Belgium': 'BE',
      'Switzerland': 'CH',
      'Austria': 'AT',
      'Sweden': 'SE',
      'Norway': 'NO',
      'Denmark': 'DK',
      'Finland': 'FI',
      'Poland': 'PL',
      'Czech Republic': 'CZ',
      'Hungary': 'HU',
      'Romania': 'RO',
      'Bulgaria': 'BG',
      'Greece': 'GR',
      'Turkey': 'TR',
      'Israel': 'IL',
      'Saudi Arabia': 'SA',
      'UAE': 'AE',
      'Iran': 'IR',
      'Iraq': 'IQ',
      'Pakistan': 'PK',
      'Bangladesh': 'BD',
      'Sri Lanka': 'LK',
      'Myanmar': 'MM',
      'South Korea': 'KR',
      'North Korea': 'KP',
      'Taiwan': 'TW',
      'Hong Kong': 'HK',
      'Macau': 'MO'
    };

    // Check if it's already a 2-letter code
    if (countryName.length === 2) {
      return countryName.toUpperCase();
    }

    // Look up in the map
    const code = countryMap[countryName];
    if (code) {
      return code;
    }

    // If not found, try to extract first 2 letters as fallback
    return countryName.substring(0, 2).toUpperCase();
  }

  // Helper method to determine current season based on latitude
  getCurrentSeasonName(latitude) {
    const month = new Date().getMonth() + 1; // 1-12
    const isNorthern = latitude > 0;

    if (Math.abs(latitude) < 23.5) {
      // Tropical regions
      return month >= 6 && month <= 9 ? 'Monsoon' : 'Dry Season';
    }

    if (isNorthern) {
      if (month >= 3 && month <= 5) return 'Spring';
      if (month >= 6 && month <= 8) return 'Summer';
      if (month >= 9 && month <= 11) return 'Autumn';
      return 'Winter';
    } else {
      // Southern hemisphere - seasons are opposite
      if (month >= 3 && month <= 5) return 'Autumn';
      if (month >= 6 && month <= 8) return 'Winter';
      if (month >= 9 && month <= 11) return 'Spring';
      return 'Summer';
    }
  }
}
