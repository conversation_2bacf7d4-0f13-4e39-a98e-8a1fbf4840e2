// Weather-Activity Impact Mapping Service
// Maps weather conditions to farming activity impacts and provides rescheduling recommendations

export class WeatherActivityMapper {
  constructor() {
    // Activity categories with weather sensitivity profiles
    this.activityProfiles = {
      // Field Operations
      'planting': {
        category: 'field_operations',
        weather_sensitivity: {
          temperature: { min: 5, max: 35, optimal: [15, 25] },
          humidity: { min: 30, max: 85, optimal: [50, 70] },
          rain_probability: { max: 20, avoid_above: 40 },
          wind_speed: { max: 15, avoid_above: 20 },
          conditions_avoid: ['heavy rain', 'storm', 'hail', 'snow']
        },
        impact_factors: {
          temperature: 0.8,
          soil_moisture: 0.9,
          rain_probability: 0.9,
          wind_speed: 0.6
        },
        rescheduling_priority: 'high'
      },

      'harvesting': {
        category: 'field_operations',
        weather_sensitivity: {
          temperature: { min: 0, max: 40, optimal: [10, 30] },
          humidity: { min: 20, max: 80, optimal: [40, 65] },
          rain_probability: { max: 15, avoid_above: 30 },
          wind_speed: { max: 20, avoid_above: 25 },
          conditions_avoid: ['rain', 'heavy rain', 'storm', 'hail']
        },
        impact_factors: {
          rain_probability: 1.0,
          humidity: 0.7,
          temperature: 0.6,
          wind_speed: 0.5
        },
        rescheduling_priority: 'critical'
      },

      'spraying': {
        category: 'chemical_operations',
        weather_sensitivity: {
          temperature: { min: 5, max: 30, optimal: [15, 25] },
          humidity: { min: 40, max: 90, optimal: [60, 80] },
          rain_probability: { max: 10, avoid_above: 20 },
          wind_speed: { max: 10, avoid_above: 15 },
          conditions_avoid: ['rain', 'heavy rain', 'storm', 'strong wind']
        },
        impact_factors: {
          wind_speed: 1.0,
          rain_probability: 0.95,
          temperature: 0.8,
          humidity: 0.7
        },
        rescheduling_priority: 'critical'
      },

      'fertilizing': {
        category: 'soil_operations',
        weather_sensitivity: {
          temperature: { min: 0, max: 35, optimal: [10, 28] },
          humidity: { min: 30, max: 90, optimal: [50, 75] },
          rain_probability: { max: 30, avoid_above: 60 },
          wind_speed: { max: 15, avoid_above: 20 },
          conditions_avoid: ['heavy rain', 'storm', 'hail']
        },
        impact_factors: {
          rain_probability: 0.8,
          wind_speed: 0.7,
          temperature: 0.6,
          humidity: 0.5
        },
        rescheduling_priority: 'medium'
      },

      'irrigation': {
        category: 'water_management',
        weather_sensitivity: {
          temperature: { min: 0, max: 45, optimal: [15, 35] },
          humidity: { min: 20, max: 95, optimal: [40, 70] },
          rain_probability: { max: 50, avoid_above: 70 },
          wind_speed: { max: 25, avoid_above: 30 },
          conditions_avoid: ['heavy rain', 'storm']
        },
        impact_factors: {
          rain_probability: 0.9,
          temperature: 0.7,
          humidity: 0.6,
          wind_speed: 0.4
        },
        rescheduling_priority: 'medium'
      },

      'pruning': {
        category: 'maintenance',
        weather_sensitivity: {
          temperature: { min: -5, max: 35, optimal: [5, 25] },
          humidity: { min: 30, max: 85, optimal: [45, 70] },
          rain_probability: { max: 25, avoid_above: 40 },
          wind_speed: { max: 20, avoid_above: 25 },
          conditions_avoid: ['heavy rain', 'storm', 'hail', 'snow']
        },
        impact_factors: {
          rain_probability: 0.7,
          temperature: 0.6,
          wind_speed: 0.5,
          humidity: 0.4
        },
        rescheduling_priority: 'low'
      },

      'soil_preparation': {
        category: 'soil_operations',
        weather_sensitivity: {
          temperature: { min: 0, max: 40, optimal: [10, 30] },
          humidity: { min: 30, max: 80, optimal: [45, 65] },
          rain_probability: { max: 20, avoid_above: 35 },
          wind_speed: { max: 25, avoid_above: 30 },
          conditions_avoid: ['heavy rain', 'storm', 'hail']
        },
        impact_factors: {
          soil_moisture: 0.9,
          rain_probability: 0.8,
          temperature: 0.6,
          wind_speed: 0.5
        },
        rescheduling_priority: 'high'
      }
    };

    // Weather condition severity levels
    this.severityLevels = {
      'optimal': { score: 1.0, description: 'Ideal conditions for activity' },
      'good': { score: 0.8, description: 'Good conditions with minor considerations' },
      'caution': { score: 0.6, description: 'Proceed with caution and monitoring' },
      'poor': { score: 0.4, description: 'Poor conditions, consider rescheduling' },
      'avoid': { score: 0.2, description: 'Avoid activity, high risk of failure' },
      'prohibited': { score: 0.0, description: 'Activity should not be performed' }
    };
  }

  // Analyze weather impact on a specific activity
  analyzeWeatherImpact(activityType, weatherData) {
    const profile = this.activityProfiles[activityType.toLowerCase()];
    if (!profile) {
      return {
        activity: activityType,
        impact_level: 'unknown',
        recommendation: 'No weather profile available for this activity',
        score: 0.5
      };
    }

    const impacts = [];
    let overallScore = 1.0;

    // Temperature impact
    if (profile.weather_sensitivity.temperature) {
      const tempImpact = this.assessTemperatureImpact(
        weatherData.temperature.value,
        profile.weather_sensitivity.temperature
      );
      impacts.push(tempImpact);
      overallScore *= (1 - (1 - tempImpact.score) * profile.impact_factors.temperature);
    }

    // Humidity impact
    if (profile.weather_sensitivity.humidity) {
      const humidityImpact = this.assessHumidityImpact(
        weatherData.humidity,
        profile.weather_sensitivity.humidity
      );
      impacts.push(humidityImpact);
      overallScore *= (1 - (1 - humidityImpact.score) * profile.impact_factors.humidity);
    }

    // Rain probability impact
    if (profile.weather_sensitivity.rain_probability) {
      const rainImpact = this.assessRainImpact(
        weatherData.rain_probability || 0,
        profile.weather_sensitivity.rain_probability
      );
      impacts.push(rainImpact);
      overallScore *= (1 - (1 - rainImpact.score) * profile.impact_factors.rain_probability);
    }

    // Wind speed impact
    if (profile.weather_sensitivity.wind_speed) {
      const windImpact = this.assessWindImpact(
        weatherData.wind_speed,
        profile.weather_sensitivity.wind_speed
      );
      impacts.push(windImpact);
      overallScore *= (1 - (1 - windImpact.score) * profile.impact_factors.wind_speed);
    }

    // Weather conditions impact
    const conditionsImpact = this.assessConditionsImpact(
      weatherData.conditions,
      profile.weather_sensitivity.conditions_avoid
    );
    impacts.push(conditionsImpact);
    overallScore *= conditionsImpact.score;

    // Determine overall impact level
    const impactLevel = this.getImpactLevel(overallScore);

    return {
      activity: activityType,
      category: profile.category,
      impact_level: impactLevel.level,
      impact_score: overallScore,
      recommendation: this.generateRecommendation(impactLevel, profile, impacts),
      rescheduling_priority: profile.rescheduling_priority,
      detailed_impacts: impacts,
      weather_timestamp: weatherData.timestamp,
      analysis_timestamp: new Date().toISOString()
    };
  }

  // Assess temperature impact
  assessTemperatureImpact(temperature, tempProfile) {
    if (temperature < tempProfile.min || temperature > tempProfile.max) {
      return {
        parameter: 'temperature',
        value: temperature,
        impact: 'prohibited',
        score: 0.0,
        message: `Temperature ${temperature}°C is outside safe range (${tempProfile.min}-${tempProfile.max}°C)`
      };
    }

    if (tempProfile.optimal) {
      const [optMin, optMax] = tempProfile.optimal;
      if (temperature >= optMin && temperature <= optMax) {
        return {
          parameter: 'temperature',
          value: temperature,
          impact: 'optimal',
          score: 1.0,
          message: `Temperature ${temperature}°C is in optimal range`
        };
      }
    }

    // Calculate score based on distance from optimal range
    const distanceFromOptimal = tempProfile.optimal 
      ? Math.min(
          Math.abs(temperature - tempProfile.optimal[0]),
          Math.abs(temperature - tempProfile.optimal[1])
        )
      : 0;

    const score = Math.max(0.4, 1.0 - (distanceFromOptimal / 10));

    return {
      parameter: 'temperature',
      value: temperature,
      impact: score > 0.8 ? 'good' : score > 0.6 ? 'caution' : 'poor',
      score: score,
      message: `Temperature ${temperature}°C is acceptable but not optimal`
    };
  }

  // Assess humidity impact
  assessHumidityImpact(humidity, humidityProfile) {
    if (humidity < humidityProfile.min || humidity > humidityProfile.max) {
      return {
        parameter: 'humidity',
        value: humidity,
        impact: 'poor',
        score: 0.3,
        message: `Humidity ${humidity}% is outside preferred range`
      };
    }

    if (humidityProfile.optimal) {
      const [optMin, optMax] = humidityProfile.optimal;
      if (humidity >= optMin && humidity <= optMax) {
        return {
          parameter: 'humidity',
          value: humidity,
          impact: 'optimal',
          score: 1.0,
          message: `Humidity ${humidity}% is optimal`
        };
      }
    }

    return {
      parameter: 'humidity',
      value: humidity,
      impact: 'good',
      score: 0.8,
      message: `Humidity ${humidity}% is acceptable`
    };
  }

  // Assess rain probability impact
  assessRainImpact(rainProbability, rainProfile) {
    if (rainProbability > rainProfile.avoid_above) {
      return {
        parameter: 'rain_probability',
        value: rainProbability,
        impact: 'prohibited',
        score: 0.0,
        message: `Rain probability ${rainProbability}% is too high for this activity`
      };
    }

    if (rainProbability <= rainProfile.max) {
      return {
        parameter: 'rain_probability',
        value: rainProbability,
        impact: 'optimal',
        score: 1.0,
        message: `Low rain probability ${rainProbability}% is favorable`
      };
    }

    const score = Math.max(0.2, 1.0 - ((rainProbability - rainProfile.max) / 30));

    return {
      parameter: 'rain_probability',
      value: rainProbability,
      impact: score > 0.6 ? 'caution' : 'poor',
      score: score,
      message: `Rain probability ${rainProbability}% requires monitoring`
    };
  }

  // Assess wind speed impact
  assessWindImpact(windSpeed, windProfile) {
    if (windSpeed > windProfile.avoid_above) {
      return {
        parameter: 'wind_speed',
        value: windSpeed,
        impact: 'prohibited',
        score: 0.0,
        message: `Wind speed ${windSpeed} m/s is too high for safe operation`
      };
    }

    if (windSpeed <= windProfile.max) {
      return {
        parameter: 'wind_speed',
        value: windSpeed,
        impact: 'optimal',
        score: 1.0,
        message: `Wind speed ${windSpeed} m/s is acceptable`
      };
    }

    const score = Math.max(0.3, 1.0 - ((windSpeed - windProfile.max) / 10));

    return {
      parameter: 'wind_speed',
      value: windSpeed,
      impact: score > 0.6 ? 'caution' : 'poor',
      score: score,
      message: `Wind speed ${windSpeed} m/s requires caution`
    };
  }

  // Assess weather conditions impact
  assessConditionsImpact(conditions, avoidConditions) {
    const conditionsLower = conditions.toLowerCase();
    
    for (const avoidCondition of avoidConditions) {
      if (conditionsLower.includes(avoidCondition.toLowerCase())) {
        return {
          parameter: 'conditions',
          value: conditions,
          impact: 'prohibited',
          score: 0.0,
          message: `Weather conditions "${conditions}" are not suitable for this activity`
        };
      }
    }

    return {
      parameter: 'conditions',
      value: conditions,
      impact: 'optimal',
      score: 1.0,
      message: `Weather conditions "${conditions}" are suitable`
    };
  }

  // Get impact level from score
  getImpactLevel(score) {
    if (score >= 0.9) return { level: 'optimal', description: this.severityLevels.optimal.description };
    if (score >= 0.7) return { level: 'good', description: this.severityLevels.good.description };
    if (score >= 0.5) return { level: 'caution', description: this.severityLevels.caution.description };
    if (score >= 0.3) return { level: 'poor', description: this.severityLevels.poor.description };
    if (score > 0.0) return { level: 'avoid', description: this.severityLevels.avoid.description };
    return { level: 'prohibited', description: this.severityLevels.prohibited.description };
  }

  // Generate activity recommendation
  generateRecommendation(impactLevel, profile, impacts) {
    const recommendations = [];

    if (impactLevel.level === 'optimal') {
      recommendations.push('Proceed with activity as planned');
    } else if (impactLevel.level === 'good') {
      recommendations.push('Good conditions for activity with minor precautions');
    } else if (impactLevel.level === 'caution') {
      recommendations.push('Proceed with caution and monitor weather closely');
    } else if (impactLevel.level === 'poor') {
      recommendations.push('Consider rescheduling to better weather conditions');
    } else if (impactLevel.level === 'avoid') {
      recommendations.push('Avoid activity, high risk of poor results');
    } else {
      recommendations.push('Do not perform activity under current conditions');
    }

    // Add specific recommendations based on impacts
    const criticalImpacts = impacts.filter(impact => 
      impact.impact === 'prohibited' || impact.impact === 'poor'
    );

    criticalImpacts.forEach(impact => {
      if (impact.parameter === 'rain_probability') {
        recommendations.push('Wait for lower rain probability');
      } else if (impact.parameter === 'wind_speed') {
        recommendations.push('Wait for calmer wind conditions');
      } else if (impact.parameter === 'temperature') {
        recommendations.push('Wait for more suitable temperature');
      }
    });

    return recommendations.join('. ');
  }

  // Get all supported activities
  getSupportedActivities() {
    return Object.keys(this.activityProfiles).map(activity => ({
      name: activity,
      category: this.activityProfiles[activity].category,
      rescheduling_priority: this.activityProfiles[activity].rescheduling_priority
    }));
  }

  // Analyze multiple activities for weather impact
  analyzeMultipleActivities(activities, weatherData) {
    return activities.map(activity => ({
      activity: activity,
      analysis: this.analyzeWeatherImpact(activity, weatherData)
    }));
  }
}
