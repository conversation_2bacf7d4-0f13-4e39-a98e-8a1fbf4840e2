// Schema definitions for structured outputs

export const LocationContextSchema = {
  type: "json_schema",
  json_schema: {
    name: "location_context",
    schema: {
      type: "object",
      properties: {
        location: {
          type: "object",
          properties: {
            latitude: { type: "number" },
            longitude: { type: "number" },
            address: { type: "string" },
            city: { type: "string" },
            state: { type: "string" },
            country: { type: "string" }
          },
          required: ["latitude", "longitude", "address", "city", "state", "country"]
        },
        climate_zone: {
          type: "object",
          properties: {
            zone_name: { type: "string" },
            zone_code: { type: "string" },
            characteristics: { type: "string" },
            typical_rainfall: { type: "string" },
            temperature_range: { type: "string" }
          },
          required: ["zone_name", "zone_code", "characteristics"]
        },
        soil_analysis: {
          type: "object",
          properties: {
            primary_type: { type: "string" },
            characteristics: { type: "string" },
            ph_range: { type: "string" },
            suitable_crops: {
              type: "array",
              items: { type: "string" }
            }
          },
          required: ["primary_type", "characteristics"]
        },
        current_season: {
          type: "object",
          properties: {
            name: { type: "string" },
            months: { type: "string" },
            weather_pattern: { type: "string" },
            farming_significance: { type: "string" }
          },
          required: ["name", "months", "weather_pattern"]
        }
      },
      required: ["location", "climate_zone", "soil_analysis", "current_season"]
    }
  }
};

export const CropContextSchema = {
  type: "json_schema",
  json_schema: {
    name: "crop_context",
    schema: {
      type: "object",
      properties: {
        crop_info: {
          type: "object",
          properties: {
            name: { type: "string" },
            variety: { type: "string" },
            lifecycle_days: { type: "number" },
            current_stage: { type: "string" },
            stage_description: { type: "string" },
            days_since_planting: { type: "number" },
            days_to_harvest: { type: "number" }
          },
          required: ["name", "lifecycle_days", "current_stage", "days_since_planting"]
        },
        stage_details: {
          type: "object",
          properties: {
            stage_number: { type: "number" },
            stage_name: { type: "string" },
            duration_days: { type: "number" },
            week_in_stage: { type: "number" },
            critical_activities: {
              type: "array",
              items: { type: "string" }
            }
          },
          required: ["stage_number", "stage_name", "week_in_stage"]
        },
        recommendations: {
          type: "object",
          properties: {
            immediate_actions: {
              type: "array",
              items: { type: "string" }
            },
            upcoming_milestones: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  activity: { type: "string" },
                  timing: { type: "string" }
                }
              }
            }
          }
        }
      },
      required: ["crop_info", "stage_details", "recommendations"]
    }
  }
};

export const WeeklyScheduleSchema = {
  type: "json_schema",
  json_schema: {
    name: "weekly_farm_schedule",
    schema: {
      type: "object",
      properties: {
        week_info: {
          type: "object",
          properties: {
            week_number: { type: "integer" },
            date_range: { type: "string" },
            year: { type: "integer" },
            status: { 
              type: "string",
              enum: ["Active", "Upcoming", "Completed"]
            }
          },
          required: ["week_number", "date_range", "year", "status"]
        },
        weather: {
          type: "object",
          properties: {
            conditions: { type: "string" },
            temperature: {
              type: "object",
              properties: {
                value: { type: "number" },
                unit: { type: "string" }
              }
            },
            humidity: { type: "string" },
            priority: {
              type: "string",
              enum: ["High", "Low", "Medium"]
            },
            weather_note: { type: "string" }
          },
          required: ["conditions", "temperature", "humidity", "priority"]
        },
        activity: {
          type: "object",
          properties: {
            title: { type: "string" },
            description: { type: "string" },
            category: { type: "string" },
            priority: {
              type: "string",
              enum: ["High", "Medium", "Low"]
            },
            estimated_time: { type: "string" }
          },
          required: ["title", "description"]
        },
        step_by_step_instructions: {
          type: "array",
          items: { type: "string" },
          description: "Detailed step-by-step instructions for the weekly activity"
        },
        required_resources: {
          type: "array",
          items: {
            type: "object",
            properties: {
              name: { type: "string" },
              quantity: { type: "string" },
              type: {
                type: "string",
                enum: ["Tool", "Material", "Equipment", "Input", "Labor"]
              },
              notes: { type: "string" }
            },
            required: ["name", "type"]
          },
          description: "List of resources needed for the weekly activity"
        },
        timing_considerations: {
          type: "object",
          properties: {
            best_time_of_day: { type: "string" },
            weather_dependencies: { type: "string" },
            duration_estimate: { type: "string" }
          }
        },
        success_indicators: {
          type: "array",
          items: { type: "string" },
          description: "How to know if the activity was successful"
        }
      },
      required: ["week_info", "weather", "activity", "step_by_step_instructions", "required_resources"]
    }
  }
};

// YouTube Videos Schema
export const YouTubeVideosSchema = {
  type: "json_schema",
  json_schema: {
    name: "youtube_videos",
    schema: {
      type: "object",
      properties: {
        videos: {
          type: "array",
          items: {
            type: "object",
            properties: {
              title: { type: "string" },
              topics: {
                type: "array",
                items: { type: "string" }
              },
              duration: { type: "string" },
              difficulty_level: {
                type: "string",
                enum: ["Beginner", "Intermediate", "Advanced"]
              },
              search_keywords: {
                type: "array",
                items: { type: "string" }
              }
            },
            required: ["title", "topics", "duration", "difficulty_level", "search_keywords"]
          }
        }
      },
      required: ["videos"]
    }
  }
};

// Pest and Disease Schema
export const PestDiseaseSchema = {
  type: "json_schema",
  json_schema: {
    name: "pest_disease_info",
    schema: {
      type: "object",
      properties: {
        alerts: {
          type: "array",
          items: {
            type: "object",
            properties: {
              name: { type: "string" },
              scientific_name: { type: "string" },
              type: {
                type: "string",
                enum: ["Pest", "Disease", "Disorder"]
              },
              severity_risk: {
                type: "string",
                enum: ["High", "Medium", "Low"]
              },
              symptoms: {
                type: "object",
                properties: {
                  early_stage: {
                    type: "array",
                    items: { type: "string" }
                  },
                  advanced_stage: {
                    type: "array",
                    items: { type: "string" }
                  },
                  affected_parts: {
                    type: "array",
                    items: { type: "string" }
                  }
                },
                required: ["early_stage", "advanced_stage", "affected_parts"]
              },
              favorable_conditions: {
                type: "array",
                items: { type: "string" }
              },
              monitoring: {
                type: "object",
                properties: {
                  when_to_scout: { type: "string" },
                  where_to_look: { type: "string" },
                  threshold: { type: "string" }
                },
                required: ["when_to_scout", "where_to_look"]
              },
              management: {
                type: "object",
                properties: {
                  cultural: {
                    type: "array",
                    items: { type: "string" }
                  },
                  biological: {
                    type: "array",
                    items: { type: "string" }
                  },
                  mechanical: {
                    type: "array",
                    items: { type: "string" }
                  },
                  chemical: {
                    type: "array",
                    items: { type: "string" }
                  },
                  preventive: {
                    type: "array",
                    items: { type: "string" }
                  }
                },
                required: ["cultural", "preventive"]
              },
              image_descriptions: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    view_type: { type: "string" },
                    description: { type: "string" }
                  }
                }
              }
            },
            required: ["name", "type", "severity_risk", "symptoms", "management"]
          }
        }
      },
      required: ["alerts"]
    }
  }
};
